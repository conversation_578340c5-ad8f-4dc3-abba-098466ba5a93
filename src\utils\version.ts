// 版本信息管理

export interface VersionInfo {
  version: string;
  buildDate: string;
  buildTime: string;
  environment: string;
}

// 获取版本信息
export const getVersionInfo = (): VersionInfo => {
  // 从环境变量或配置中获取版本信息
  const version = import.meta.env.VITE_APP_VERSION || '1.0.0';
  const buildDate = import.meta.env.VITE_BUILD_DATE || new Date().toLocaleDateString('zh-CN');
  const buildTime = import.meta.env.VITE_BUILD_TIME || new Date().toLocaleTimeString('zh-CN');
  const environment = import.meta.env.MODE || 'development';

  return {
    version,
    buildDate,
    buildTime,
    environment,
  };
};

// 获取简化的版本字符串
export const getVersionString = (): string => {
  const { version } = getVersionInfo();
  return `v${version}`;
};

// 获取完整的版本信息字符串
export const getFullVersionString = (): string => {
  const { version, buildDate, environment } = getVersionInfo();
  return `v${version} (${environment}) - ${buildDate}`;
};

// 获取应用信息
export const getAppInfo = () => {
  return {
    name: '次席管理平台',
    fullName: '次席管理平台',
    description: '专业的金融交易导航平台',
    version: getVersionString(),
    copyright: `© ${new Date().getFullYear()} 长江期货股份有限公司`,
  };
};
