@echo off
chcp 65001 >nul

echo 🚀 启动 次席管理平台 服务器...

REM 检查Node.js版本
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Node.js，请先安装 Node.js 16+ 版本
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js 版本: %NODE_VERSION%

REM 进入服务器目录
cd /d "%~dp0"

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 安装依赖包...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 创建数据目录
if not exist "data" mkdir data
echo 📁 数据目录已准备: %cd%\data

REM 设置环境变量
if "%NODE_ENV%"=="" set NODE_ENV=production
if "%PORT%"=="" set PORT=3001

echo 🔧 环境配置:
echo    - 环境: %NODE_ENV%
echo    - 端口: %PORT%
echo    - 数据目录: %cd%\data

REM 启动服务器
echo 🌟 启动服务器...
if "%NODE_ENV%"=="development" (
    call npm run dev
) else (
    call npm start
)

pause
