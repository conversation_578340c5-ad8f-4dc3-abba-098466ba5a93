# 侧边栏版本号显示功能

## 🎯 功能概述

在左侧边栏底部添加了版本信息显示区域，展示应用的版本号、名称和版权信息。

## 🔧 实现方案

### 1. 版本管理工具 (`src/utils/version.ts`)

创建了专门的版本管理工具，提供统一的版本信息获取接口：

```typescript
export interface VersionInfo {
  version: string;
  buildDate: string;
  buildTime: string;
  environment: string;
}

export const getAppInfo = () => {
  return {
    name: 'CJFCO Nav',
    fullName: '长江期货导航系统',
    description: '专业的金融交易导航平台',
    version: getVersionString(),
    copyright: `© ${new Date().getFullYear()} 长江期货有限公司`,
  };
};
```

**功能特性**：
- ✅ **动态版本号**：支持从环境变量读取版本信息
- ✅ **构建信息**：包含构建日期和时间
- ✅ **环境标识**：区分开发/生产环境
- ✅ **应用信息**：统一管理应用名称和描述

### 2. 侧边栏布局调整

修改了 Sidebar 组件的布局结构，使用 Flexbox 布局：

```tsx
<div className="w-64 bg-white border-r border-gray-200 h-screen flex flex-col">
  {/* 主要内容区域 - 可滚动 */}
  <div className="flex-1 overflow-y-auto">
    <div className="p-6">
      {/* 原有的侧边栏内容 */}
    </div>
  </div>

  {/* 版本信息 - 固定在底部 */}
  <div className="flex-shrink-0 p-4 border-t border-gray-200 bg-gray-50">
    {/* 版本信息内容 */}
  </div>
</div>
```

**布局特点**：
- ✅ **Flex 容器**：使用 `flex flex-col` 创建垂直布局
- ✅ **主内容区**：`flex-1 overflow-y-auto` 允许内容滚动
- ✅ **固定底部**：`flex-shrink-0` 确保版本信息固定在底部
- ✅ **视觉分离**：使用边框和背景色区分版本信息区域

### 3. 版本信息显示

版本信息区域包含以下内容：

```tsx
<div className="text-center">
  <div className="text-xs text-gray-400 mb-1">
    {appInfo.name}
  </div>
  <div className="text-xs text-blue-600 font-mono font-semibold">
    {appInfo.version}
  </div>
  <div className="text-xs text-gray-400 mt-1">
    {appInfo.fullName}
  </div>
  <div className="text-xs text-gray-300 mt-1">
    {appInfo.copyright}
  </div>
</div>
```

**显示内容**：
- 📱 **应用简称**：CJFCO Nav
- 🔢 **版本号**：v1.0.0 (蓝色高亮显示)
- 📝 **应用全名**：长江期货导航系统
- ©️ **版权信息**：© 2024 长江期货有限公司

## 🎨 视觉设计

### 样式特点

**背景和边框**：
- 🎨 **背景色**：`bg-gray-50` 浅灰色背景
- 📏 **边框**：`border-t border-gray-200` 顶部分割线
- 📐 **内边距**：`p-4` 适当的内边距

**文字样式**：
- 📝 **应用名称**：`text-xs text-gray-400` 小号灰色文字
- 🔢 **版本号**：`text-xs text-blue-600 font-mono font-semibold` 蓝色等宽字体
- 📄 **描述文字**：`text-xs text-gray-400` 小号灰色文字
- ©️ **版权信息**：`text-xs text-gray-300` 更浅的灰色

**布局对齐**：
- 🎯 **居中对齐**：`text-center` 所有文字居中显示
- 📏 **间距控制**：使用 `mb-1` 和 `mt-1` 控制行间距

### 颜色方案

| 元素 | 颜色类 | 颜色值 | 用途 |
|------|--------|--------|------|
| 应用名称 | `text-gray-400` | #9CA3AF | 次要信息 |
| 版本号 | `text-blue-600` | #2563EB | 重点突出 |
| 描述文字 | `text-gray-400` | #9CA3AF | 次要信息 |
| 版权信息 | `text-gray-300` | #D1D5DB | 辅助信息 |
| 背景色 | `bg-gray-50` | #F9FAFB | 区域分离 |
| 边框色 | `border-gray-200` | #E5E7EB | 视觉分割 |

## 📱 响应式适配

版本信息区域在不同屏幕尺寸下都能正常显示：

- ✅ **桌面端**：完整显示所有信息
- ✅ **平板端**：保持良好的可读性
- ✅ **移动端**：文字大小适中，不会过小

## 🔧 配置和扩展

### 环境变量支持

可以通过环境变量自定义版本信息：

```bash
# .env 文件
VITE_APP_VERSION=1.0.0
VITE_BUILD_DATE=2024-01-01
VITE_BUILD_TIME=12:00:00
```

### 版本号格式

支持多种版本号格式：
- ✅ **语义化版本**：v1.0.0
- ✅ **构建版本**：v1.0.0-build.123
- ✅ **环境标识**：v1.0.0-dev

### 扩展功能

可以轻松扩展更多信息：

```typescript
// 添加更多应用信息
export const getAppInfo = () => {
  return {
    // ... 现有信息
    buildNumber: getBuildNumber(),
    lastUpdate: getLastUpdateDate(),
    supportEmail: '<EMAIL>',
  };
};
```

## 🎯 用户体验

### 信息层次

版本信息按重要性分层显示：
1. **应用名称** - 品牌识别
2. **版本号** - 核心信息（蓝色突出）
3. **完整名称** - 详细描述
4. **版权信息** - 法律信息

### 视觉平衡

- ✅ **不抢夺焦点**：使用较小的字体和低对比度颜色
- ✅ **信息完整**：提供必要的版本和版权信息
- ✅ **专业外观**：符合企业级应用的视觉标准

## 🔄 维护和更新

### 版本更新流程

1. **修改版本号**：更新 `package.json` 或环境变量
2. **构建应用**：`npm run build`
3. **自动更新**：版本信息会自动反映在界面上

### 版本号管理

建议使用语义化版本控制：
- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

通过这个版本显示功能，用户可以清楚地了解当前使用的应用版本，便于问题反馈和技术支持。
