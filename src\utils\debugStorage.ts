// 调试工具：检查和管理本地存储数据

export const debugStorage = {
  // 查看所有存储的数据
  viewAll: () => {
    console.group('🔍 本地存储数据检查');
    
    // 检查主要数据存储
    const mainData = localStorage.getItem('fintech-nav-storage');
    if (mainData) {
      try {
        const parsed = JSON.parse(mainData);
        console.log('📦 主要数据 (fintech-nav-storage):', parsed);
        console.log('  - 应用数量:', parsed.state?.items?.length || 0);
        console.log('  - 分类数量:', parsed.state?.categories?.length || 0);
        console.log('  - 用户信息:', parsed.state?.currentUser ? '已设置' : '未设置');
        console.log('  - 视图模式:', parsed.state?.viewMode || '未设置');
      } catch (error) {
        console.error('❌ 主要数据解析失败:', error);
      }
    } else {
      console.log('❌ 未找到主要数据存储');
    }
    
    // 检查会话数据
    const sessionData = localStorage.getItem('fintech-nav-session');
    if (sessionData) {
      try {
        const parsed = JSON.parse(sessionData);
        console.log('🔐 会话数据 (fintech-nav-session):', parsed);
      } catch (error) {
        console.error('❌ 会话数据解析失败:', error);
      }
    } else {
      console.log('❌ 未找到会话数据');
    }
    
    // 检查所有相关的存储键
    const allKeys = Object.keys(localStorage).filter(key => 
      key.includes('fintech') || key.includes('nav')
    );
    console.log('🔑 所有相关存储键:', allKeys);
    
    console.groupEnd();
  },

  // 清除所有数据
  clearAll: () => {
    const keys = Object.keys(localStorage).filter(key => 
      key.includes('fintech') || key.includes('nav')
    );
    
    keys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`🗑️ 已删除: ${key}`);
    });
    
    console.log('✅ 所有相关数据已清除');
    console.log('🔄 请刷新页面以重新初始化');
  },

  // 导出数据
  exportData: () => {
    const mainData = localStorage.getItem('fintech-nav-storage');
    if (mainData) {
      const blob = new Blob([mainData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `fintech-nav-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      console.log('💾 数据已导出');
    } else {
      console.log('❌ 没有数据可导出');
    }
  },

  // 导入数据
  importData: (jsonString: string) => {
    try {
      const data = JSON.parse(jsonString);
      localStorage.setItem('fintech-nav-storage', JSON.stringify(data));
      console.log('📥 数据已导入');
      console.log('🔄 请刷新页面以应用更改');
    } catch (error) {
      console.error('❌ 数据导入失败:', error);
    }
  },

  // 检查数据完整性
  checkIntegrity: () => {
    console.group('🔍 数据完整性检查');
    
    const mainData = localStorage.getItem('fintech-nav-storage');
    if (!mainData) {
      console.log('❌ 主要数据不存在');
      console.groupEnd();
      return false;
    }

    try {
      const parsed = JSON.parse(mainData);
      const state = parsed.state;
      
      if (!state) {
        console.log('❌ 状态数据不存在');
        return false;
      }

      // 检查必要字段
      const checks = [
        { name: 'items', value: state.items, type: 'array' },
        { name: 'categories', value: state.categories, type: 'array' },
        { name: 'currentUser', value: state.currentUser, type: 'object' },
        { name: 'viewMode', value: state.viewMode, type: 'string' },
      ];

      let allValid = true;
      checks.forEach(check => {
        const isValid = check.type === 'array' 
          ? Array.isArray(check.value)
          : typeof check.value === check.type;
        
        if (isValid) {
          console.log(`✅ ${check.name}: 有效`);
        } else {
          console.log(`❌ ${check.name}: 无效 (期望: ${check.type}, 实际: ${typeof check.value})`);
          allValid = false;
        }
      });

      console.log(`📊 总体状态: ${allValid ? '✅ 完整' : '❌ 有问题'}`);
      console.groupEnd();
      return allValid;
    } catch (error) {
      console.error('❌ 数据解析失败:', error);
      console.groupEnd();
      return false;
    }
  },

  // 重置为默认数据
  resetToDefault: () => {
    debugStorage.clearAll();
    console.log('🔄 数据已重置，请刷新页面');
  },

  // 检查收藏数据一致性
  checkFavoriteConsistency: () => {
    console.group('⭐ 收藏数据一致性检查');

    const mainData = localStorage.getItem('fintech-nav-storage');
    if (!mainData) {
      console.log('❌ 未找到主要数据');
      console.groupEnd();
      return;
    }

    try {
      const parsed = JSON.parse(mainData);
      const state = parsed.state;

      if (!state || !state.items || !state.currentUser) {
        console.log('❌ 数据结构不完整');
        console.groupEnd();
        return;
      }

      const items = state.items;
      const currentUser = state.currentUser;
      const userFavorites = currentUser.favorites || [];

      console.log('📊 收藏统计：');
      console.log(`  - 用户收藏列表长度: ${userFavorites.length}`);
      console.log(`  - 用户收藏列表: [${userFavorites.join(', ')}]`);

      // 检查应用的 isFavorite 状态
      const favoriteItems = items.filter(item => item.isFavorite);
      console.log(`  - 标记为收藏的应用数量: ${favoriteItems.length}`);
      console.log(`  - 标记为收藏的应用ID: [${favoriteItems.map(item => item.id).join(', ')}]`);

      // 检查一致性
      const inconsistentItems = [];
      items.forEach(item => {
        const inUserFavorites = userFavorites.includes(item.id);
        const markedAsFavorite = item.isFavorite;

        if (inUserFavorites !== markedAsFavorite) {
          inconsistentItems.push({
            id: item.id,
            name: item.name,
            inUserFavorites,
            markedAsFavorite,
          });
        }
      });

      if (inconsistentItems.length === 0) {
        console.log('✅ 收藏数据一致性检查通过');
      } else {
        console.log('❌ 发现数据不一致：');
        inconsistentItems.forEach(item => {
          console.log(`  - ${item.name} (${item.id}): 用户列表=${item.inUserFavorites}, 应用标记=${item.markedAsFavorite}`);
        });
      }

      // 检查孤立的收藏ID
      const orphanedFavorites = userFavorites.filter(favId =>
        !items.some(item => item.id === favId)
      );

      if (orphanedFavorites.length > 0) {
        console.log('⚠️ 发现孤立的收藏ID（应用不存在）：');
        console.log(`  - [${orphanedFavorites.join(', ')}]`);
      }

      console.groupEnd();

      return {
        userFavoritesCount: userFavorites.length,
        markedFavoritesCount: favoriteItems.length,
        inconsistentItems,
        orphanedFavorites,
        isConsistent: inconsistentItems.length === 0 && orphanedFavorites.length === 0,
      };
    } catch (error) {
      console.error('❌ 数据解析失败:', error);
      console.groupEnd();
      return null;
    }
  }
};

// 在开发环境中将调试工具添加到全局对象
if (import.meta.env.DEV) {
  (window as any).debugStorage = debugStorage;
  console.log('🛠️ 调试工具已加载，使用 debugStorage.viewAll() 查看数据');
}
