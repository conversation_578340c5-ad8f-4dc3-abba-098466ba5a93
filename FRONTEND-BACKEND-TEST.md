# 前后端连接测试指南

## 🎯 测试目标

验证前端应用是否正确连接到后端API，确保数据能够正确同步，解决"清除浏览器缓存后显示初始数据"的问题。

## 🚀 当前状态

### 后端服务器
- ✅ **状态**: 运行中
- 🌐 **地址**: http://localhost:3001
- 📊 **API**: http://localhost:3001/api

### 前端服务器
- ✅ **状态**: 运行中
- 🌐 **地址**: http://localhost:5174
- 🔄 **API集成**: 已配置

## 🔧 已完成的修改

### 1. 创建了新的API Store (`src/store/useApiStore.ts`)
- ✅ 优先从后端API获取数据
- ✅ 支持在线/离线模式
- ✅ 自动数据同步
- ✅ 错误处理和重试机制

### 2. 创建了数据同步服务 (`src/services/apiClient.ts`)
- ✅ HTTP客户端封装
- ✅ 在线/离线兼容
- ✅ 离线操作队列
- ✅ 自动同步机制

### 3. 更新了所有组件
- ✅ Header - 使用 `useApiStore`
- ✅ Sidebar - 使用 `useApiStore`
- ✅ NavItemGrid - 使用 `useApiStore`
- ✅ NavItemCard - 使用 `useApiStore`
- ✅ 所有模态框组件 - 使用 `useApiStore`

### 4. 创建了新的App组件 (`src/components/AppWithApi.tsx`)
- ✅ 数据加载状态管理
- ✅ 在线/离线状态指示
- ✅ 错误提示显示
- ✅ 自动数据初始化

## 🧪 测试步骤

### 方法一：浏览器开发者工具测试

1. **打开前端应用**: http://localhost:5174

2. **打开开发者工具** (F12)

3. **检查网络请求**:
   - 切换到 Network 标签
   - 刷新页面
   - 查看是否有对 `http://localhost:3001/api` 的请求

4. **检查控制台日志**:
   - 切换到 Console 标签
   - 查看是否有数据加载日志
   - 应该看到类似 "🌐 从服务器加载数据..." 的消息

### 方法二：手动API测试

在浏览器控制台中运行以下代码：

```javascript
// 测试API连接
fetch('http://localhost:3001/health')
  .then(response => response.json())
  .then(data => console.log('健康检查:', data))
  .catch(error => console.error('连接失败:', error));

// 测试获取导航项目
fetch('http://localhost:3001/api/nav-items')
  .then(response => response.json())
  .then(data => console.log('导航项目:', data))
  .catch(error => console.error('获取失败:', error));

// 测试添加导航项目
fetch('http://localhost:3001/api/nav-items', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: '测试应用',
    url: 'https://example.com',
    description: '测试描述',
    category: 'all',
    tags: ['测试'],
    icon: 'Globe'
  })
})
.then(response => response.json())
.then(data => console.log('添加成功:', data))
.catch(error => console.error('添加失败:', error));
```

### 方法三：功能测试

1. **添加应用测试**:
   - 点击侧边栏的 "+" 按钮
   - 填写应用信息并保存
   - 检查是否出现在列表中

2. **删除应用测试**:
   - 进入管理模式 (点击设置按钮)
   - 删除一个应用
   - 刷新页面，检查应用是否真的被删除

3. **数据持久化测试**:
   - 添加几个应用
   - 清除浏览器缓存 (Ctrl+Shift+Delete)
   - 刷新页面
   - 检查数据是否仍然存在

## 🔍 故障排除

### 问题1: 前端无法连接到后端

**症状**: 控制台显示网络错误，页面显示 "📱 离线" 状态

**解决方案**:
```bash
# 检查后端服务器是否运行
curl http://localhost:3001/health

# 如果没有运行，启动后端服务器
cd server
npm run dev
```

### 问题2: CORS 错误

**症状**: 控制台显示 CORS 相关错误

**解决方案**:
- 后端已配置允许所有来源 (开发环境)
- 检查后端服务器是否正常运行
- 确认前端请求的URL正确

### 问题3: 数据不同步

**症状**: 前端操作后，刷新页面数据没有保存

**检查步骤**:
1. 打开开发者工具 Network 标签
2. 执行操作 (添加/删除应用)
3. 检查是否有对应的API请求
4. 检查请求是否成功 (状态码200)
5. 检查后端数据文件是否更新

### 问题4: 页面显示加载中

**症状**: 页面一直显示 "正在加载数据..."

**解决方案**:
1. 检查后端服务器状态
2. 检查控制台错误信息
3. 检查网络连接
4. 重启前后端服务器

## 📊 预期行为

### 正常情况下的数据流

1. **页面加载**:
   ```
   前端启动 → 调用 loadData() → 检查服务器状态 → 从API获取数据 → 显示数据
   ```

2. **添加应用**:
   ```
   用户操作 → 调用 addNavItem() → 发送POST请求 → 更新本地状态 → 界面更新
   ```

3. **删除应用**:
   ```
   用户操作 → 调用 deleteNavItem() → 发送DELETE请求 → 更新本地状态 → 界面更新
   ```

4. **数据持久化**:
   ```
   API操作 → 后端更新内存 → 自动保存到文件 → 数据持久化完成
   ```

### 状态指示器

- 🌐 **在线**: 成功连接到后端API
- 📱 **离线**: 无法连接到后端，使用本地数据
- ⚠️ **错误**: 显示具体的错误信息

## 🎯 测试检查清单

- [ ] 后端服务器正常运行 (http://localhost:3001/health)
- [ ] 前端服务器正常运行 (http://localhost:5174)
- [ ] 页面显示 "🌐 在线" 状态
- [ ] 能够添加新应用
- [ ] 能够删除应用
- [ ] 清除缓存后数据仍然存在
- [ ] 网络请求正常 (开发者工具 Network 标签)
- [ ] 控制台无错误信息

## 🔄 如果测试失败

如果测试失败，请按以下步骤操作：

1. **重启后端服务器**:
   ```bash
   cd server
   # 停止当前进程 (Ctrl+C)
   npm run dev
   ```

2. **重启前端服务器**:
   ```bash
   # 停止当前进程 (Ctrl+C)
   npm run dev
   ```

3. **清除浏览器缓存**:
   - 按 Ctrl+Shift+Delete
   - 清除所有数据
   - 刷新页面

4. **检查控制台日志**:
   - 查看详细的错误信息
   - 根据错误信息进行相应的修复

现在您可以按照这个指南来测试前后端连接是否正常工作！
