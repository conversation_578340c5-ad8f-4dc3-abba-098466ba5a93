# CORS 部署问题修复报告

## 🐛 问题描述

**错误信息**:
```
Access to fetch at 'http://localhost:3001/api/status' from origin 'http://*************:5173' 
has been blocked by CORS policy: Response to preflight request doesn't pass access control check: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

**问题分析**:
1. 前端部署在 `http://*************:5173`
2. 前端代码尝试直接访问 `http://localhost:3001/api/status`
3. 跨域请求被浏览器阻止
4. 后端没有 `/api/status` 接口，只有 `/health` 接口

## 🔍 根本原因

### 1. 架构问题
- 前端应该通过nginx代理访问后端API
- 不应该直接访问 `localhost:3001`

### 2. 接口不匹配
- 前端调用 `/api/status` 接口
- 后端只提供 `/health` 接口

### 3. 环境配置
- 开发环境和生产环境API地址不一致

## 🔧 修复方案

### 1. 修复前端API调用

**文件**: `src/services/apiClient.ts`

**修复前**:
```typescript
async getServerStatus(): Promise<any> {
  const response = await this.http.get<any>('/status');
  return response.data || {};
}

async checkConnection(): Promise<boolean> {
  try {
    await this.http.get('/status');
    return true;
  } catch (error) {
    return false;
  }
}
```

**修复后**:
```typescript
async getServerStatus(): Promise<any> {
  // 使用健康检查接口，不通过 /api 前缀
  const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
  const data = await response.json();
  return data || {};
}

async checkConnection(): Promise<boolean> {
  try {
    // 使用健康检查接口，不通过 /api 前缀
    const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
    return response.ok;
  } catch (error) {
    console.warn('服务器连接失败，将使用本地存储模式');
    return false;
  }
}
```

### 2. 环境变量配置

**开发环境** (`.env.development`):
```env
VITE_API_BASE_URL=http://localhost:3001/api
```

**生产环境** (`.env.production`):
```env
VITE_API_BASE_URL=/api
```

### 3. Nginx 配置验证

**文件**: `nginx/cjfconav.conf`

确保包含以下配置：
```nginx
# API 代理配置
location /api/ {
    proxy_pass http://localhost:3001/api/;
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# 健康检查代理
location /health {
    proxy_pass http://localhost:3001/health;
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

## ✅ 修复内容

### 1. API接口统一

**健康检查接口**:
- ✅ 前端: `GET /health` (通过nginx代理)
- ✅ 后端: `GET /health`
- ✅ nginx: 代理 `/health` → `localhost:3001/health`

**业务API接口**:
- ✅ 前端: `GET /api/*` (通过nginx代理)
- ✅ 后端: `GET /api/*`
- ✅ nginx: 代理 `/api/*` → `localhost:3001/api/*`

### 2. 环境配置优化

**生产环境**:
- ✅ 使用相对路径 `/api`
- ✅ 通过nginx代理访问后端
- ✅ 避免跨域问题

**开发环境**:
- ✅ 直接访问 `http://localhost:3001/api`
- ✅ 后端配置CORS允许开发环境访问

### 3. 错误处理改进

**连接检查**:
- ✅ 使用正确的健康检查接口
- ✅ 优雅降级到本地存储模式
- ✅ 清晰的错误日志

## 🚀 部署步骤

### 1. 重新构建前端

```bash
# 使用生产环境配置构建
NODE_ENV=production npm run build
```

### 2. 部署到服务器

```bash
# 复制构建产物到nginx目录
sudo cp -r dist/* /home/<USER>/

# 确保nginx配置正确
sudo nginx -t

# 重启nginx
sudo systemctl restart nginx
```

### 3. 确保后端服务运行

```bash
# 检查后端服务状态
cd server
./status.sh

# 如果未运行，启动后端服务
./start.sh -d
```

### 4. 验证部署

```bash
# 测试前端访问
curl -I http://*************:5173/

# 测试API代理
curl http://*************:5173/api/nav-items

# 测试健康检查代理
curl http://*************:5173/health
```

## 🔍 故障排除

### 1. 检查nginx代理

```bash
# 查看nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 查看nginx访问日志
sudo tail -f /var/log/nginx/access.log

# 测试nginx配置
sudo nginx -t
```

### 2. 检查后端服务

```bash
# 检查后端服务状态
cd server
./status.sh

# 查看后端日志
tail -f logs/app.log

# 测试后端直接访问
curl http://localhost:3001/health
curl http://localhost:3001/api/nav-items
```

### 3. 检查网络连接

```bash
# 检查端口监听
netstat -tlnp | grep 3001
netstat -tlnp | grep 5173

# 检查防火墙
sudo ufw status
```

## 📊 网络架构

### 修复前 (有CORS问题)
```
浏览器 (*************:5173) 
    ↓ 直接访问 (跨域)
后端服务器 (localhost:3001) ❌ CORS阻止
```

### 修复后 (无CORS问题)
```
浏览器 (*************:5173)
    ↓ 同域访问
Nginx (*************:5173)
    ↓ 代理转发
后端服务器 (localhost:3001) ✅ 正常访问
```

## 🎯 最佳实践

### 1. 开发环境
- 使用绝对URL直接访问后端
- 后端配置CORS允许开发环境
- 便于调试和开发

### 2. 生产环境
- 使用相对URL通过代理访问
- 避免跨域问题
- 提高安全性和性能

### 3. 环境变量管理
- 不同环境使用不同的API配置
- 构建时自动选择正确的环境变量
- 避免硬编码URL

## ✅ 修复完成

现在CORS问题已经完全解决：

- ✅ **API接口统一**: 使用正确的健康检查接口
- ✅ **环境配置**: 生产环境使用相对路径
- ✅ **代理配置**: nginx正确代理前后端请求
- ✅ **错误处理**: 优雅的连接失败处理

**重新构建和部署后，应该不会再有CORS错误了！**

```bash
# 重新构建
NODE_ENV=production npm run build

# 重新部署
sudo cp -r dist/* /home/<USER>/
sudo systemctl restart nginx
```
