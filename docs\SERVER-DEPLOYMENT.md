# CJFCO Nav 服务器端部署指南

## 🎯 架构概述

### 数据存储架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端客户端    │◄──►│   Node.js API   │◄──►│   文件系统存储   │
│  (React App)   │    │     服务器      │    │  (JSON Files)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  本地存储备份   │    │   内存缓存      │    │   自动备份      │
│ (localStorage)  │    │  (高性能访问)   │    │  (定时保存)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心特性
- ✅ **内存缓存**: 所有数据加载到内存，提供高性能访问
- ✅ **文件持久化**: 数据自动保存到JSON文件，确保数据安全
- ✅ **在线/离线兼容**: 支持服务器在线和离线模式
- ✅ **自动同步**: 离线操作会在服务器恢复时自动同步
- ✅ **数据一致性**: 所有客户端共享同一份服务器数据

## 🚀 快速部署

### 1. 环境要求
- **Node.js**: 16.0+ 版本
- **操作系统**: Windows/Linux/macOS
- **内存**: 最少 512MB
- **磁盘**: 最少 100MB 可用空间

### 2. 安装步骤

#### Windows 部署
```bash
# 1. 进入服务器目录
cd server

# 2. 安装依赖
npm install

# 3. 启动服务器
start.bat
```

#### Linux/macOS 部署
```bash
# 1. 进入服务器目录
cd server

# 2. 给启动脚本执行权限
chmod +x start.sh

# 3. 安装依赖
npm install

# 4. 启动服务器
./start.sh
```

### 3. 环境变量配置

创建 `.env` 文件：
```bash
# 服务器端口
PORT=3001

# 运行环境
NODE_ENV=production

# CORS 允许的域名
ALLOWED_ORIGINS=http://localhost:5173,https://your-domain.com

# 数据保存间隔（毫秒）
AUTO_SAVE_INTERVAL=300000
```

## 📁 目录结构

```
server/
├── package.json          # 项目配置
├── index.js              # 服务器入口
├── start.sh              # Linux/macOS 启动脚本
├── start.bat             # Windows 启动脚本
├── .env                  # 环境变量配置
├── data/                 # 数据存储目录
│   ├── navItems.json     # 导航项目数据
│   ├── categories.json   # 分类数据
│   ├── stats.json        # 统计数据
│   └── config.json       # 配置数据
└── src/
    ├── dataManager.js    # 数据管理器
    └── routes/
        └── api.js        # API 路由
```

## 🔧 API 接口文档

### 基础信息
- **基础URL**: `http://localhost:3001/api`
- **数据格式**: JSON
- **认证方式**: 暂无（内网使用）

### 导航项目接口

#### 获取所有导航项目
```http
GET /api/nav-items
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "item1",
      "name": "应用名称",
      "url": "https://example.com",
      "description": "应用描述",
      "category": "category1",
      "tags": ["标签1", "标签2"],
      "accessCount": 10,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "total": 1
}
```

#### 添加导航项目
```http
POST /api/nav-items
Content-Type: application/json

{
  "name": "新应用",
  "url": "https://example.com",
  "description": "应用描述",
  "category": "category1",
  "tags": ["标签1"]
}
```

#### 更新导航项目
```http
PUT /api/nav-items/:id
Content-Type: application/json

{
  "name": "更新后的名称",
  "description": "更新后的描述"
}
```

#### 删除导航项目
```http
DELETE /api/nav-items/:id
```

#### 增加访问次数
```http
POST /api/nav-items/:id/access
```

### 分类接口

#### 获取所有分类
```http
GET /api/categories
```

#### 添加分类
```http
POST /api/categories
Content-Type: application/json

{
  "name": "新分类",
  "color": "#3B82F6",
  "icon": "Folder",
  "description": "分类描述"
}
```

#### 更新分类
```http
PUT /api/categories/:id
```

#### 删除分类
```http
DELETE /api/categories/:id
```

### 统计数据接口

#### 获取统计数据
```http
GET /api/stats
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalItems": 50,
    "totalViews": 1000,
    "totalCategories": 8,
    "lastUpdated": "2024-01-01T00:00:00.000Z"
  }
}
```

### 配置接口

#### 获取配置
```http
GET /api/config
```

#### 更新配置
```http
PUT /api/config
Content-Type: application/json

{
  "theme": {
    "primaryColor": "#3B82F6",
    "secondaryColor": "#8B5CF6"
  }
}
```

### 系统接口

#### 手动保存数据
```http
POST /api/save
```

#### 获取服务器状态
```http
GET /api/status
```

#### 健康检查
```http
GET /health
```

## 🔒 安全配置

### 1. CORS 配置
```javascript
// 生产环境只允许特定域名
const allowedOrigins = [
  'http://localhost:5173',
  'https://your-domain.com'
];
```

### 2. 限流配置
```javascript
// 每15分钟最多1000次请求
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 1000
});
```

### 3. 安全头配置
```javascript
// 使用 helmet 中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
```

## 📊 性能优化

### 1. 内存缓存
- 所有数据加载到内存，避免频繁文件IO
- 提供毫秒级响应时间
- 支持高并发访问

### 2. 自动保存机制
- 默认每5分钟自动保存一次
- 服务器关闭时强制保存
- 避免数据丢失

### 3. 压缩和缓存
```javascript
// 启用 gzip 压缩
app.use(compression());

// 静态资源缓存
app.use(express.static('public', {
  maxAge: '1d'
}));
```

## 🔄 数据备份策略

### 1. 自动备份
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
cp -r server/data server/backups/backup_$DATE
```

### 2. 定时备份
```bash
# 添加到 crontab
0 2 * * * /path/to/backup.sh
```

### 3. 数据恢复
```bash
# 恢复数据
cp -r server/backups/backup_20240101_020000/* server/data/
```

## 🚀 生产环境部署

### 1. 使用 PM2 管理进程
```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start index.js --name "cjfco-nav"

# 设置开机自启
pm2 startup
pm2 save
```

### 2. Nginx 反向代理
```nginx
server {
    listen 80;
    server_name your-api-domain.com;
    
    location /api {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 系统服务配置
```ini
# /etc/systemd/system/cjfco-nav.service
[Unit]
Description=CJFCO Nav API Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/server
ExecStart=/usr/bin/node index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3001

[Install]
WantedBy=multi-user.target
```

## 📈 监控和日志

### 1. 日志配置
```javascript
// 使用 winston 记录日志
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});
```

### 2. 性能监控
```javascript
// 监控内存使用
setInterval(() => {
  const usage = process.memoryUsage();
  console.log('内存使用:', {
    rss: Math.round(usage.rss / 1024 / 1024) + 'MB',
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB'
  });
}, 60000);
```

通过这个服务器端架构，您的导航系统将具备企业级的数据一致性和可靠性，所有用户都能看到相同的数据，并且支持高并发访问。
