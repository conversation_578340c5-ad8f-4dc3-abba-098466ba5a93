import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';
import { AppState, NavItem, Category, User, Stats } from '../types';
import { mockData } from '../data/mockData';

interface AppStore extends AppState {
  // Actions
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: string) => void;
  setShowFavorites: (show: boolean) => void;
  setViewMode: (mode: 'grid' | 'list') => void;
  setAdminMode: (isAdmin: boolean) => void;
  
  // Nav Items
  addNavItem: (item: Omit<NavItem, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateNavItem: (id: string, updates: Partial<NavItem>) => void;
  deleteNavItem: (id: string) => void;
  incrementAccessCount: (id: string) => void;
  toggleLike: (id: string) => void;
  toggleFavorite: (id: string) => void;
  
  // Categories
  addCategory: (category: Omit<Category, 'id' | 'itemCount'>) => void;
  updateCategory: (id: string, updates: Partial<Category>) => void;
  deleteCategory: (id: string) => void;
  
  // User
  setCurrentUser: (user: User | null) => void;
  
  // Computed
  getFilteredItems: () => NavItem[];
  getStats: () => Stats;
  getCategoryStats: () => Category[];
}

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Initial state
      items: mockData.items,
      categories: mockData.categories,
      currentUser: mockData.currentUser,
      searchQuery: '',
      selectedCategory: 'all',
      showFavorites: false,
      isAdminMode: false,
      viewMode: 'grid',
      
      // Actions
      setSearchQuery: (query) => set({ searchQuery: query }),
      setSelectedCategory: (category) => set({ selectedCategory: category }),
      setShowFavorites: (show) => set({ showFavorites: show }),
      setViewMode: (mode) => set({ viewMode: mode }),
      setAdminMode: (isAdmin) => set({ isAdminMode: isAdmin }),
      
      // Nav Items
      addNavItem: (item) => {
        const newItem: NavItem = {
          ...item,
          id: uuidv4(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        set((state) => ({
          items: [...state.items, newItem],
        }));
      },
      
      updateNavItem: (id, updates) => {
        set((state) => ({
          items: state.items.map((item) =>
            item.id === id
              ? { ...item, ...updates, updatedAt: new Date().toISOString() }
              : item
          ),
        }));
      },
      
      deleteNavItem: (id) => {
        set((state) => ({
          items: state.items.filter((item) => item.id !== id),
        }));
      },
      
      incrementAccessCount: (id) => {
        set((state) => ({
          items: state.items.map((item) =>
            item.id === id
              ? { ...item, accessCount: item.accessCount + 1 }
              : item
          ),
        }));
      },
      
      toggleLike: (id) => {
        set((state) => ({
          items: state.items.map((item) =>
            item.id === id
              ? { ...item, likeCount: item.likeCount + (item.likeCount > 0 ? -1 : 1) }
              : item
          ),
        }));
      },
      
      toggleFavorite: (id) => {
        const { currentUser } = get();
        if (!currentUser) return;
        
        set((state) => ({
          items: state.items.map((item) =>
            item.id === id
              ? { ...item, isFavorite: !item.isFavorite }
              : item
          ),
          currentUser: {
            ...currentUser,
            favorites: currentUser.favorites.includes(id)
              ? currentUser.favorites.filter((fav) => fav !== id)
              : [...currentUser.favorites, id],
          },
        }));
      },
      
      // Categories
      addCategory: (category) => {
        const newCategory: Category = {
          ...category,
          id: uuidv4(),
          itemCount: 0,
        };
        set((state) => ({
          categories: [...state.categories, newCategory],
        }));
      },
      
      updateCategory: (id, updates) => {
        set((state) => ({
          categories: state.categories.map((cat) =>
            cat.id === id ? { ...cat, ...updates } : cat
          ),
        }));
      },
      
      deleteCategory: (id) => {
        set((state) => ({
          categories: state.categories.filter((cat) => cat.id !== id),
        }));
      },
      
      // User
      setCurrentUser: (user) => set({ currentUser: user }),
      
      // Computed
      getFilteredItems: () => {
        const { items, searchQuery, selectedCategory, showFavorites, currentUser } = get();
        
        let filtered = items;
        
        // Filter by search query
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          filtered = filtered.filter((item) =>
            item.name.toLowerCase().includes(query) ||
            item.description.toLowerCase().includes(query) ||
            item.tags.some((tag) => tag.toLowerCase().includes(query))
          );
        }
        
        // Filter by category
        if (selectedCategory !== 'all') {
          filtered = filtered.filter((item) => item.category === selectedCategory);
        }
        
        // Filter by favorites
        if (showFavorites && currentUser) {
          filtered = filtered.filter((item) => currentUser.favorites.includes(item.id));
        }
        
        return filtered.sort((a, b) => b.accessCount - a.accessCount);
      },
      
      getStats: () => {
        const { items } = get();
        const totalViews = items.reduce((sum, item) => sum + item.accessCount, 0);
        const totalLikes = items.reduce((sum, item) => sum + item.likeCount, 0);
        const mostPopular = [...items]
          .sort((a, b) => b.accessCount - a.accessCount)
          .slice(0, 5);
        const recentlyAdded = [...items]
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 5);
        
        return {
          totalItems: items.length,
          totalCategories: get().categories.length,
          totalViews,
          totalLikes,
          mostPopular,
          recentlyAdded,
        };
      },
      
      getCategoryStats: () => {
        const { categories, items } = get();
        return categories.map((category) => ({
          ...category,
          itemCount: items.filter((item) => item.category === category.id).length,
        }));
      },
    }),
    {
      name: 'fintech-nav-storage',
      partialize: (state) => ({
        items: state.items,
        categories: state.categories,
        currentUser: state.currentUser,
        viewMode: state.viewMode,
      }),
    }
  )
);