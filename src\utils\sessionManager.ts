import { configManager } from './config';

export interface SessionData {
  userId: string;
  loginTime: number;
  lastActivity: number;
  expiresAt: number;
}

export class SessionManager {
  private static instance: SessionManager;
  private sessionKey = 'fintech-nav-session';
  private activityCheckInterval: NodeJS.Timeout | null = null;
  private warningShown = false;
  private onSessionExpired?: () => void;
  private onSessionWarning?: (remainingTime: number) => void;

  private constructor() {
    this.startActivityMonitoring();
    this.bindActivityEvents();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  // 设置会话过期回调
  public setSessionExpiredCallback(callback: () => void): void {
    this.onSessionExpired = callback;
  }

  // 设置会话警告回调
  public setSessionWarningCallback(callback: (remainingTime: number) => void): void {
    this.onSessionWarning = callback;
  }

  // 创建新会话
  public createSession(userId: string): void {
    const config = configManager.getConfig();
    const now = Date.now();
    const sessionTimeout = config.security.sessionTimeout * 1000; // 转换为毫秒

    const sessionData: SessionData = {
      userId,
      loginTime: now,
      lastActivity: now,
      expiresAt: now + sessionTimeout,
    };

    localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
    this.warningShown = false;
    console.log('Session created for user:', userId);
  }

  // 更新会话活动时间
  public updateActivity(): void {
    const session = this.getSession();
    if (!session) return;

    const config = configManager.getConfig();
    const now = Date.now();
    const sessionTimeout = config.security.sessionTimeout * 1000;

    session.lastActivity = now;
    session.expiresAt = now + sessionTimeout;

    localStorage.setItem(this.sessionKey, JSON.stringify(session));
    this.warningShown = false;
  }

  // 获取当前会话
  public getSession(): SessionData | null {
    try {
      const sessionStr = localStorage.getItem(this.sessionKey);
      if (!sessionStr) return null;

      const session: SessionData = JSON.parse(sessionStr);
      
      // 检查会话是否过期
      if (Date.now() > session.expiresAt) {
        this.destroySession();
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error parsing session data:', error);
      this.destroySession();
      return null;
    }
  }

  // 检查会话是否有效
  public isSessionValid(): boolean {
    const session = this.getSession();
    return session !== null;
  }

  // 获取会话剩余时间（毫秒）
  public getRemainingTime(): number {
    const session = this.getSession();
    if (!session) return 0;

    return Math.max(0, session.expiresAt - Date.now());
  }

  // 销毁会话
  public destroySession(): void {
    localStorage.removeItem(this.sessionKey);
    this.warningShown = false;
    console.log('Session destroyed');
  }

  // 开始活动监控
  private startActivityMonitoring(): void {
    // 每30秒检查一次会话状态
    this.activityCheckInterval = setInterval(() => {
      this.checkSessionStatus();
    }, 30000);
  }

  // 停止活动监控
  public stopActivityMonitoring(): void {
    if (this.activityCheckInterval) {
      clearInterval(this.activityCheckInterval);
      this.activityCheckInterval = null;
    }
  }

  // 检查会话状态
  private checkSessionStatus(): void {
    const session = this.getSession();
    if (!session) {
      // 会话已过期或不存在
      if (this.onSessionExpired) {
        this.onSessionExpired();
      }
      return;
    }

    const remainingTime = this.getRemainingTime();
    const warningThreshold = 5 * 60 * 1000; // 5分钟警告

    // 如果剩余时间少于5分钟且未显示警告
    if (remainingTime <= warningThreshold && !this.warningShown) {
      this.warningShown = true;
      if (this.onSessionWarning) {
        this.onSessionWarning(Math.ceil(remainingTime / 60000)); // 转换为分钟
      }
    }

    // 如果会话已过期
    if (remainingTime <= 0) {
      this.destroySession();
      if (this.onSessionExpired) {
        this.onSessionExpired();
      }
    }
  }

  // 绑定用户活动事件
  private bindActivityEvents(): void {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const activityHandler = () => {
      if (this.isSessionValid()) {
        this.updateActivity();
      }
    };

    // 使用防抖来避免过于频繁的更新
    let debounceTimer: NodeJS.Timeout;
    const debouncedHandler = () => {
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(activityHandler, 1000); // 1秒防抖
    };

    events.forEach(event => {
      document.addEventListener(event, debouncedHandler, true);
    });
  }

  // 延长会话
  public extendSession(): void {
    const session = this.getSession();
    if (!session) return;

    const config = configManager.getConfig();
    const sessionTimeout = config.security.sessionTimeout * 1000;
    const now = Date.now();

    session.lastActivity = now;
    session.expiresAt = now + sessionTimeout;

    localStorage.setItem(this.sessionKey, JSON.stringify(session));
    this.warningShown = false;
    console.log('Session extended');
  }

  // 获取会话信息
  public getSessionInfo(): { userId: string; remainingTime: number; loginTime: number } | null {
    const session = this.getSession();
    if (!session) return null;

    return {
      userId: session.userId,
      remainingTime: this.getRemainingTime(),
      loginTime: session.loginTime,
    };
  }
}

// 导出单例实例
export const sessionManager = SessionManager.getInstance();
