// 创建示例数据的脚本

const API_BASE_URL = 'http://localhost:3001/api';

// 示例分类数据
const sampleCategories = [
  {
    name: '交易系统',
    color: '#1890ff',
    icon: 'TrendingUp',
    description: '股票、期货、外汇等交易平台'
  },
  {
    name: '风险管理',
    color: '#f5222d',
    icon: 'Shield',
    description: '风险控制和合规管理系统'
  },
  {
    name: '数据分析',
    color: '#52c41a',
    icon: 'BarChart3',
    description: '市场数据分析和研究工具'
  },
  {
    name: '客户服务',
    color: '#722ed1',
    icon: 'Users',
    description: '客户关系管理和服务系统'
  },
  {
    name: '办公工具',
    color: '#fa8c16',
    icon: 'Briefcase',
    description: '日常办公和协作工具'
  }
];

// 示例导航项目数据
const sampleNavItems = [
  // 交易系统
  {
    name: '期货交易系统',
    url: 'https://trading.cjfco.com.cn',
    description: '专业的期货交易平台，支持多品种交易',
    category: 'trading',
    tags: ['期货', '交易', '实时'],
    icon: 'TrendingUp'
  },
  {
    name: '股票交易终端',
    url: 'https://stock.cjfco.com.cn',
    description: '股票交易专用终端，提供实时行情和交易功能',
    category: 'trading',
    tags: ['股票', '交易', '终端'],
    icon: 'LineChart'
  },
  {
    name: '外汇交易平台',
    url: 'https://forex.cjfco.com.cn',
    description: '外汇和贵金属交易平台',
    category: 'trading',
    tags: ['外汇', '贵金属', '交易'],
    icon: 'DollarSign'
  },
  {
    name: '期权交易系统',
    url: 'https://options.cjfco.com.cn',
    description: '期权交易和策略分析系统',
    category: 'trading',
    tags: ['期权', '策略', '分析'],
    icon: 'Target'
  },

  // 风险管理
  {
    name: '风险监控中心',
    url: 'https://risk.cjfco.com.cn',
    description: '实时风险监控和预警系统',
    category: 'risk',
    tags: ['风险', '监控', '预警'],
    icon: 'Shield'
  },
  {
    name: '合规管理系统',
    url: 'https://compliance.cjfco.com.cn',
    description: '合规检查和报告管理系统',
    category: 'risk',
    tags: ['合规', '检查', '报告'],
    icon: 'FileCheck'
  },
  {
    name: '资金监管平台',
    url: 'https://fund-monitor.cjfco.com.cn',
    description: '客户资金安全监管平台',
    category: 'risk',
    tags: ['资金', '监管', '安全'],
    icon: 'Lock'
  },

  // 数据分析
  {
    name: '市场数据中心',
    url: 'https://data.cjfco.com.cn',
    description: '实时市场数据和历史数据查询',
    category: 'analysis',
    tags: ['数据', '市场', '查询'],
    icon: 'Database'
  },
  {
    name: '量化分析平台',
    url: 'https://quant.cjfco.com.cn',
    description: '量化策略开发和回测平台',
    category: 'analysis',
    tags: ['量化', '策略', '回测'],
    icon: 'Calculator'
  },
  {
    name: '研究报告系统',
    url: 'https://research.cjfco.com.cn',
    description: '投资研究报告发布和管理',
    category: 'analysis',
    tags: ['研究', '报告', '投资'],
    icon: 'FileText'
  },
  {
    name: '技术分析工具',
    url: 'https://chart.cjfco.com.cn',
    description: '专业的技术分析图表工具',
    category: 'analysis',
    tags: ['技术分析', '图表', '工具'],
    icon: 'BarChart3'
  },

  // 客户服务
  {
    name: '客户管理系统',
    url: 'https://crm.cjfco.com.cn',
    description: '客户关系管理和服务系统',
    category: 'service',
    tags: ['客户', '管理', '服务'],
    icon: 'Users'
  },
  {
    name: '在线客服平台',
    url: 'https://support.cjfco.com.cn',
    description: '在线客户服务和技术支持',
    category: 'service',
    tags: ['客服', '支持', '在线'],
    icon: 'MessageCircle'
  },
  {
    name: '开户管理系统',
    url: 'https://account.cjfco.com.cn',
    description: '客户开户和账户管理系统',
    category: 'service',
    tags: ['开户', '账户', '管理'],
    icon: 'UserPlus'
  },

  // 办公工具
  {
    name: '企业邮箱',
    url: 'https://mail.cjfco.com.cn',
    description: '企业内部邮件系统',
    category: 'office',
    tags: ['邮箱', '邮件', '通讯'],
    icon: 'Mail'
  },
  {
    name: '文档管理系统',
    url: 'https://docs.cjfco.com.cn',
    description: '企业文档存储和协作平台',
    category: 'office',
    tags: ['文档', '存储', '协作'],
    icon: 'FolderOpen'
  },
  {
    name: '视频会议系统',
    url: 'https://meeting.cjfco.com.cn',
    description: '企业视频会议和远程协作',
    category: 'office',
    tags: ['会议', '视频', '协作'],
    icon: 'Video'
  },
  {
    name: '项目管理平台',
    url: 'https://project.cjfco.com.cn',
    description: '项目进度跟踪和任务管理',
    category: 'office',
    tags: ['项目', '管理', '任务'],
    icon: 'CheckSquare'
  },
  {
    name: '人事管理系统',
    url: 'https://hr.cjfco.com.cn',
    description: '人力资源管理和考勤系统',
    category: 'office',
    tags: ['人事', '考勤', '管理'],
    icon: 'UserCheck'
  },

  // 外部常用工具
  {
    name: '上海期货交易所',
    url: 'https://www.shfe.com.cn',
    description: '上海期货交易所官方网站',
    category: 'all',
    tags: ['期货', '交易所', '官方'],
    icon: 'Building'
  },
  {
    name: '大连商品交易所',
    url: 'https://www.dce.com.cn',
    description: '大连商品交易所官方网站',
    category: 'all',
    tags: ['商品', '交易所', '官方'],
    icon: 'Building'
  },
  {
    name: '郑州商品交易所',
    url: 'https://www.czce.com.cn',
    description: '郑州商品交易所官方网站',
    category: 'all',
    tags: ['商品', '交易所', '官方'],
    icon: 'Building'
  },
  {
    name: '中国金融期货交易所',
    url: 'https://www.cffex.com.cn',
    description: '中国金融期货交易所官方网站',
    category: 'all',
    tags: ['金融期货', '交易所', '官方'],
    icon: 'Building'
  }
];

async function createSampleData() {
  console.log('🚀 开始创建示例数据...\n');

  try {
    // 1. 创建分类
    console.log('📁 创建示例分类...');
    const createdCategories = {};
    
    for (const category of sampleCategories) {
      try {
        const response = await fetch(`${API_BASE_URL}/categories`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(category)
        });
        
        const result = await response.json();
        if (result.success) {
          createdCategories[category.name] = result.data.id;
          console.log(`✅ 创建分类: ${category.name}`);
        } else {
          console.log(`❌ 创建分类失败: ${category.name} - ${result.error}`);
        }
      } catch (error) {
        console.log(`❌ 创建分类错误: ${category.name} - ${error.message}`);
      }
    }

    // 2. 更新导航项目的分类ID
    const categoryMapping = {
      'trading': createdCategories['交易系统'],
      'risk': createdCategories['风险管理'],
      'analysis': createdCategories['数据分析'],
      'service': createdCategories['客户服务'],
      'office': createdCategories['办公工具']
    };

    // 3. 创建导航项目
    console.log('\n📱 创建示例导航项目...');
    let successCount = 0;
    
    for (const item of sampleNavItems) {
      try {
        // 更新分类ID
        if (categoryMapping[item.category]) {
          item.category = categoryMapping[item.category];
        }
        
        const response = await fetch(`${API_BASE_URL}/nav-items`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item)
        });
        
        const result = await response.json();
        if (result.success) {
          successCount++;
          console.log(`✅ 创建应用: ${item.name}`);
        } else {
          console.log(`❌ 创建应用失败: ${item.name} - ${result.error}`);
        }
      } catch (error) {
        console.log(`❌ 创建应用错误: ${item.name} - ${error.message}`);
      }
    }

    console.log(`\n🎉 示例数据创建完成!`);
    console.log(`📊 统计信息:`);
    console.log(`   - 分类: ${Object.keys(createdCategories).length}/${sampleCategories.length} 个`);
    console.log(`   - 应用: ${successCount}/${sampleNavItems.length} 个`);
    
    // 4. 获取最终统计
    const statsResponse = await fetch(`${API_BASE_URL}/stats`);
    const stats = await statsResponse.json();
    if (stats.success) {
      console.log(`\n📈 当前系统统计:`);
      console.log(`   - 总应用数: ${stats.data.totalItems}`);
      console.log(`   - 总分类数: ${stats.data.totalCategories}`);
      console.log(`   - 总访问数: ${stats.data.totalViews}`);
    }

  } catch (error) {
    console.error('❌ 创建示例数据失败:', error);
  }
}

// 运行脚本
createSampleData();
