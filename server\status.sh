#!/bin/bash

# 次席管理平台 服务器状态检查脚本

echo "📊 次席管理平台 服务器状态检查"
echo "================================"

# 进入服务器目录
cd "$(dirname "$0")"

# 日志和PID文件路径
LOG_DIR="$(pwd)/logs"
PID_FILE="$LOG_DIR/server.pid"
APP_LOG="$LOG_DIR/app.log"
DATA_DIR="$(pwd)/data"

# 解析命令行参数
SHOW_LOGS=false
SHOW_HELP=false
LOG_LINES=20

while [[ $# -gt 0 ]]; do
    case $1 in
        -l|--logs)
            SHOW_LOGS=true
            shift
            ;;
        -n|--lines)
            LOG_LINES="$2"
            shift 2
            ;;
        -h|--help)
            SHOW_HELP=true
            shift
            ;;
        *)
            echo "❌ 未知参数: $1"
            SHOW_HELP=true
            shift
            ;;
    esac
done

# 显示帮助信息
if [ "$SHOW_HELP" = true ]; then
    echo ""
    echo "📖 使用方法:"
    echo "   $0 [选项]"
    echo ""
    echo "选项:"
    echo "   -l, --logs       显示最近的日志"
    echo "   -n, --lines N    显示最近N行日志 (默认: 20)"
    echo "   -h, --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "   $0               # 显示状态"
    echo "   $0 -l            # 显示状态和日志"
    echo "   $0 -l -n 50      # 显示状态和最近50行日志"
    echo ""
    exit 0
fi

# 检查服务器进程状态
echo "🔍 进程状态:"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "   ✅ 服务器正在运行 (PID: $PID)"
        
        # 显示进程详细信息
        echo "   📋 进程信息:"
        ps -p "$PID" -o pid,ppid,user,cpu,mem,etime,cmd --no-headers | while read line; do
            echo "      $line"
        done
        
        # 检查端口占用
        PORT=${PORT:-3001}
        if netstat -tlnp 2>/dev/null | grep ":$PORT " > /dev/null; then
            echo "   🌐 端口 $PORT 正在监听"
        else
            echo "   ⚠️  端口 $PORT 未在监听"
        fi
        
        SERVER_RUNNING=true
    else
        echo "   ❌ PID文件存在但进程不在运行"
        echo "      PID文件: $PID_FILE (PID: $PID)"
        echo "      建议清理: rm $PID_FILE"
        SERVER_RUNNING=false
    fi
else
    echo "   ❌ 服务器未在后台运行 (无PID文件)"
    
    # 搜索可能的进程
    PIDS=$(pgrep -f "node.*index.js" 2>/dev/null)
    if [ -n "$PIDS" ]; then
        echo "   🔍 找到可能相关的进程:"
        for pid in $PIDS; do
            ps -p "$pid" -o pid,user,cmd --no-headers | while read line; do
                echo "      $line"
            done
        done
    fi
    
    SERVER_RUNNING=false
fi

echo ""

# 检查服务健康状态
echo "🏥 服务健康检查:"
PORT=${PORT:-3001}
if curl -s -f "http://localhost:$PORT/health" > /dev/null 2>&1; then
    echo "   ✅ 健康检查通过"
    
    # 获取健康检查详细信息
    HEALTH_INFO=$(curl -s "http://localhost:$PORT/health" 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$HEALTH_INFO" ]; then
        echo "   📊 服务信息:"
        echo "$HEALTH_INFO" | python3 -m json.tool 2>/dev/null | sed 's/^/      /' || echo "      $HEALTH_INFO"
    fi
else
    echo "   ❌ 健康检查失败"
    echo "      URL: http://localhost:$PORT/health"
fi

echo ""

# 检查文件和目录
echo "📁 文件系统状态:"

# 检查数据目录
if [ -d "$DATA_DIR" ]; then
    echo "   ✅ 数据目录存在: $DATA_DIR"
    DATA_FILES=$(find "$DATA_DIR" -name "*.json" 2>/dev/null | wc -l)
    echo "      JSON文件数量: $DATA_FILES"
    
    # 显示数据文件大小
    if [ $DATA_FILES -gt 0 ]; then
        echo "      文件详情:"
        find "$DATA_DIR" -name "*.json" -exec ls -lh {} \; 2>/dev/null | while read line; do
            echo "        $line"
        done
    fi
else
    echo "   ⚠️  数据目录不存在: $DATA_DIR"
fi

# 检查日志目录
if [ -d "$LOG_DIR" ]; then
    echo "   ✅ 日志目录存在: $LOG_DIR"
    LOG_FILES=$(find "$LOG_DIR" -name "*.log" 2>/dev/null | wc -l)
    echo "      日志文件数量: $LOG_FILES"
    
    # 显示日志文件大小
    if [ $LOG_FILES -gt 0 ]; then
        echo "      文件详情:"
        find "$LOG_DIR" -name "*.log" -exec ls -lh {} \; 2>/dev/null | while read line; do
            echo "        $line"
        done
    fi
else
    echo "   ⚠️  日志目录不存在: $LOG_DIR"
fi

echo ""

# 检查依赖
echo "🔧 环境检查:"
NODE_VERSION=$(node --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "   ✅ Node.js: $NODE_VERSION"
else
    echo "   ❌ Node.js 未安装"
fi

NPM_VERSION=$(npm --version 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "   ✅ npm: $NPM_VERSION"
else
    echo "   ❌ npm 未安装"
fi

if [ -f "package.json" ]; then
    echo "   ✅ package.json 存在"
    if [ -d "node_modules" ]; then
        echo "   ✅ 依赖已安装"
    else
        echo "   ⚠️  依赖未安装 (运行: npm install)"
    fi
else
    echo "   ❌ package.json 不存在"
fi

echo ""

# 显示日志 (如果请求)
if [ "$SHOW_LOGS" = true ] && [ -f "$APP_LOG" ]; then
    echo "📋 最近的日志 (最后 $LOG_LINES 行):"
    echo "----------------------------------------"
    tail -n "$LOG_LINES" "$APP_LOG" 2>/dev/null || echo "   (无法读取日志文件)"
    echo "----------------------------------------"
    echo ""
fi

# 显示管理命令
echo "🔧 管理命令:"
if [ "$SERVER_RUNNING" = true ]; then
    echo "   停止服务: ./stop.sh"
    echo "   重启服务: ./start.sh -f -d"
    echo "   查看日志: tail -f $APP_LOG"
else
    echo "   启动服务: ./start.sh -d"
    echo "   前台运行: ./start.sh"
fi

echo "   查看状态: ./status.sh"
echo "   查看日志: ./status.sh -l"

# 总结状态
echo ""
echo "📊 状态总结:"
if [ "$SERVER_RUNNING" = true ]; then
    if curl -s -f "http://localhost:$PORT/health" > /dev/null 2>&1; then
        echo "   🟢 服务器运行正常"
    else
        echo "   🟡 服务器运行但健康检查失败"
    fi
else
    echo "   🔴 服务器未运行"
fi
