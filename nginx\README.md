# Nginx 配置说明

## 📁 配置文件

- **配置文件**: `nginx/cjfconav.conf`
- **部署端口**: 5173
- **后端服务**: localhost:3001

## 🔧 配置特性

### 1. 前后端统一代理

**前端服务**:
- 静态文件服务 (React 构建产物)
- SPA 路由支持 (所有路由重定向到 index.html)

**后端API代理**:
- `/api/*` → `http://localhost:3001/api/*`
- `/health` → `http://localhost:3001/health`

### 2. 性能优化

**静态资源缓存**:
- CSS/JS 文件: 1年缓存
- 图片资源: 30天缓存
- Assets 目录: 1年缓存 + immutable

**Gzip 压缩**:
- 启用多种文件类型压缩
- 压缩级别: 6
- 最小压缩文件: 1024字节

### 3. 安全配置

**安全头部**:
- X-Frame-Options: SAMEORIGIN
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- Content-Security-Policy: 基础策略

**代理安全**:
- 正确的 Host 头部传递
- Real-IP 和 Forwarded-For 头部
- 协议头部传递

### 4. 错误处理

**API 错误处理**:
- 后端服务不可用时返回 503 错误
- 友好的 JSON 错误响应
- 中英文错误提示

**前端错误处理**:
- 404/500 错误重定向到 index.html
- SPA 应用路由支持

## 🚀 部署步骤

### 1. 复制配置文件

```bash
# 复制配置到 nginx 配置目录
sudo cp nginx/cjfconav.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/cjfconav.conf /etc/nginx/sites-enabled/
```

### 2. 创建部署目录

```bash
# 创建应用目录
sudo mkdir -p /home/<USER>
sudo chown -R www-data:www-data /home/<USER>
```

### 3. 部署前端文件

```bash
# 构建前端
npm run build

# 复制构建产物
sudo cp -r dist/* /home/<USER>/
```

### 4. 启动后端服务

```bash
# 进入后端目录
cd server

# 使用 PM2 启动后端服务
pm2 start index.js --name "cjfco-nav-api" --port 3001

# 或者使用 systemd 服务
sudo systemctl start cjfco-nav-backend
```

### 5. 重启 Nginx

```bash
# 测试配置
sudo nginx -t

# 重启 nginx
sudo systemctl restart nginx
```

## 🔍 配置验证

### 1. 检查服务状态

```bash
# 检查 nginx 状态
sudo systemctl status nginx

# 检查后端服务状态
pm2 status
# 或
sudo systemctl status cjfco-nav-backend
```

### 2. 测试访问

```bash
# 测试前端访问
curl -I http://localhost:5173/

# 测试 API 代理
curl http://localhost:5173/health
curl http://localhost:5173/api/nav-items

# 测试后端直接访问
curl http://localhost:3001/health
curl http://localhost:3001/api/nav-items
```

### 3. 检查日志

```bash
# 查看 nginx 访问日志
sudo tail -f /var/log/nginx/cjfconav.access.log

# 查看 nginx 错误日志
sudo tail -f /var/log/nginx/cjfconav.error.log

# 查看后端服务日志
pm2 logs cjfco-nav-api
```

## ⚙️ 配置调优

### 1. 修改端口

如需修改端口，编辑配置文件：

```nginx
server {
    listen 8080;  # 修改为所需端口
    # ...
}
```

### 2. 修改后端地址

如后端服务在不同服务器：

```nginx
location /api/ {
    proxy_pass http://backend-server:3001/api/;
    # ...
}
```

### 3. 添加 SSL 支持

```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    # ...
}
```

## 🔧 故障排除

### 1. 502 Bad Gateway

**可能原因**:
- 后端服务未启动
- 后端端口不正确
- 防火墙阻止连接

**解决方案**:
```bash
# 检查后端服务
curl http://localhost:3001/health

# 检查端口占用
netstat -tlnp | grep 3001

# 启动后端服务
cd server && npm run dev
```

### 2. 404 Not Found (API)

**可能原因**:
- API 路径配置错误
- 后端路由不存在

**解决方案**:
```bash
# 检查 nginx 配置
sudo nginx -t

# 检查后端 API
curl http://localhost:3001/api/nav-items
```

### 3. 静态文件 404

**可能原因**:
- 前端文件路径错误
- 权限问题

**解决方案**:
```bash
# 检查文件权限
ls -la /home/<USER>/

# 修复权限
sudo chown -R www-data:www-data /home/<USER>/
```

## 📊 监控建议

### 1. 日志监控

```bash
# 设置日志轮转
sudo logrotate -f /etc/logrotate.d/nginx

# 监控错误日志
sudo tail -f /var/log/nginx/cjfconav.error.log | grep -E "(error|warn)"
```

### 2. 性能监控

```bash
# 监控连接数
ss -tuln | grep :5173

# 监控进程状态
ps aux | grep nginx
pm2 monit
```

### 3. 健康检查

```bash
# 创建健康检查脚本
#!/bin/bash
curl -f http://localhost:5173/health || exit 1
curl -f http://localhost:5173/api/stats || exit 1
```

## 🎯 最佳实践

1. **定期备份配置文件**
2. **监控日志文件大小**
3. **定期更新安全头部**
4. **使用 HTTPS (生产环境)**
5. **配置适当的缓存策略**
6. **设置合理的超时时间**
7. **启用访问日志分析**

现在您的 nginx 配置已经包含了完整的前后端代理设置！
