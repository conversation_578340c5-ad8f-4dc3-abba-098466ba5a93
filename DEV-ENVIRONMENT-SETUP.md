# CJFCO Nav 开发环境设置完成

## ✅ 开发环境状态

### 🚀 后端服务器已启动
- **状态**: ✅ 运行中
- **地址**: http://localhost:3001
- **环境**: development
- **热重载**: 已启用 (nodemon)
- **数据存储**: `E:\MyFiles\CodeProgrames\FinTradeNav\server\data`

### 📊 API 接口测试结果
- ✅ **健康检查**: `GET /health` - 正常
- ✅ **导航项目**: `GET /api/nav-items` - 正常 (返回空数组)
- ✅ **分类数据**: `GET /api/categories` - 正常 (返回2个默认分类)
- ✅ **统计数据**: `GET /api/stats` - 正常
- ✅ **配置数据**: `GET /api/config` - 正常

### 📁 数据文件状态
- ✅ `data/navItems.json` - 已创建
- ✅ `data/categories.json` - 已创建 (包含默认分类)
- ✅ `data/stats.json` - 已创建
- ✅ `data/config.json` - 已创建

## 🛠️ 开发工具和脚本

### 启动脚本
```bash
# Windows
server/dev-start.bat        # 开发环境启动脚本
server/check-dev.bat        # 状态检查脚本

# Linux/macOS  
server/dev-start.sh         # 开发环境启动脚本
```

### npm 命令
```bash
cd server

# 启动开发服务器 (推荐)
npm run dev

# 启动调试模式
npm run dev:debug

# 生产模式启动
npm start
```

## 🔧 开发环境特性

### 自动重载
- ✅ **文件监听**: 监听 `src/` 和 `index.js` 文件变化
- ✅ **自动重启**: 代码修改后自动重启服务器
- ✅ **快速反馈**: 无需手动重启

### 调试支持
- 🐛 **详细错误**: 显示完整错误堆栈
- 🔍 **调试端口**: 9229 (使用 dev:debug 时)
- 📊 **开发日志**: 详细的请求响应日志

### CORS 配置
- 🌐 **宽松策略**: 开发环境允许所有来源
- 🔄 **前端支持**: 支持前端开发服务器访问

## 🌐 服务地址

### 后端服务
- **主服务**: http://localhost:3001
- **健康检查**: http://localhost:3001/health
- **API 根路径**: http://localhost:3001/api

### 前端服务 (需要单独启动)
- **开发服务器**: http://localhost:5173
- **启动命令**: `npm run dev` (在项目根目录)

## 📋 API 接口列表

### 导航项目
- `GET /api/nav-items` - 获取所有导航项目
- `POST /api/nav-items` - 创建导航项目
- `PUT /api/nav-items/:id` - 更新导航项目
- `DELETE /api/nav-items/:id` - 删除导航项目
- `POST /api/nav-items/:id/access` - 增加访问次数

### 分类管理
- `GET /api/categories` - 获取所有分类
- `POST /api/categories` - 创建分类
- `PUT /api/categories/:id` - 更新分类
- `DELETE /api/categories/:id` - 删除分类

### 数据统计
- `GET /api/stats` - 获取统计数据
- `GET /api/config` - 获取配置
- `PUT /api/config` - 更新配置

### 系统管理
- `GET /health` - 健康检查
- `GET /api/status` - 服务器状态
- `POST /api/save` - 手动保存数据

## 🧪 API 测试示例

### 使用 curl 测试
```bash
# 健康检查
curl http://localhost:3001/health

# 获取导航项目
curl http://localhost:3001/api/nav-items

# 创建导航项目
curl -X POST http://localhost:3001/api/nav-items \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试应用",
    "url": "https://example.com",
    "description": "这是一个测试应用",
    "category": "all",
    "tags": ["测试"]
  }'

# 获取分类
curl http://localhost:3001/api/categories

# 获取统计数据
curl http://localhost:3001/api/stats
```

### 使用 PowerShell 测试
```powershell
# 健康检查
Invoke-RestMethod http://localhost:3001/health

# 获取导航项目
Invoke-RestMethod http://localhost:3001/api/nav-items

# 创建导航项目
$body = @{
    name = "测试应用"
    url = "https://example.com"
    description = "这是一个测试应用"
    category = "all"
    tags = @("测试")
} | ConvertTo-Json

Invoke-RestMethod -Uri http://localhost:3001/api/nav-items -Method POST -Body $body -ContentType "application/json"
```

## 🔄 前后端联调

### 1. 启动后端服务器
```bash
cd server
npm run dev
# 或
dev-start.bat
```

### 2. 启动前端开发服务器
```bash
# 在项目根目录
npm run dev
```

### 3. 验证连接
- 前端: http://localhost:5173
- 后端: http://localhost:3001
- 前端会自动代理 `/api` 请求到后端

## 📊 数据持久化

### 存储机制
- **内存缓存**: 所有数据加载到内存，提供高性能访问
- **文件持久化**: 数据自动保存到 JSON 文件
- **自动保存**: 每5分钟自动保存一次
- **优雅关闭**: 服务器关闭时强制保存数据

### 数据文件位置
```
server/data/
├── navItems.json      # 导航项目数据
├── categories.json    # 分类数据  
├── stats.json         # 统计数据
└── config.json        # 配置数据
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :3001
   
   # 终止占用进程
   taskkill /F /PID <进程ID>
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **服务器无法启动**
   - 检查 Node.js 版本 (需要 16+)
   - 检查端口是否被占用
   - 查看错误日志

4. **API 请求失败**
   - 确认服务器已启动
   - 检查 CORS 配置
   - 验证请求格式

### 日志查看
```bash
# 实时查看服务器日志
# 开发模式下日志直接输出到控制台

# 如果有日志文件
tail -f logs/dev.log        # Linux/macOS
Get-Content logs/dev.log -Wait  # Windows PowerShell
```

## 🎯 下一步

现在开发环境已经完全配置好，您可以：

1. **启动前端开发服务器** - 在项目根目录运行 `npm run dev`
2. **开始前后端联调** - 前端会自动连接到后端API
3. **测试API功能** - 使用上面提供的测试命令
4. **开发新功能** - 修改代码会自动重启服务器

开发环境已经准备就绪！🎉
