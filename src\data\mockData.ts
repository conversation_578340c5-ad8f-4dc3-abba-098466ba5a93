import { NavItem, Category, User } from '../types';

export const mockData = {
  currentUser: {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=2',
    role: 'admin' as const,
    favorites: ['1', '3', '5'],
  },
  
  categories: [
    {
      id: 'trading',
      name: '交易系统',
      color: '#1890ff',
      icon: 'TrendingUp',
      description: '股票、期货、外汇等交易平台',
      itemCount: 0,
      // 可以编辑和删除
    },
    {
      id: 'risk',
      name: '风险管理',
      color: '#f5222d',
      icon: 'Shield',
      description: '风险控制和合规管理系统',
      itemCount: 0,
      // 可以编辑和删除
    },
    {
      id: 'analytics',
      name: '数据分析',
      color: '#52c41a',
      icon: 'BarChart3',
      description: '数据分析和商业智能平台',
      itemCount: 0,
    },
    {
      id: 'customer',
      name: '客户管理',
      color: '#722ed1',
      icon: 'Users',
      description: 'CRM和客户服务系统',
      itemCount: 0,
    },
    {
      id: 'finance',
      name: '财务管理',
      color: '#fa8c16',
      icon: 'Calculator',
      description: '会计、财务和结算系统',
      itemCount: 0,
    },
    {
      id: 'tools',
      name: '开发工具',
      color: '#13c2c2',
      icon: 'Settings',
      description: '开发和运维工具',
      itemCount: 0,
    },
  ] as Category[],
  
  items: [
    {
      id: '1',
      name: '智能交易平台',
      description: '基于AI的量化交易系统，支持多种交易策略和风险控制',
      url: 'https://trading.example.com',
      icon: 'TrendingUp',
      category: 'trading',
      tags: ['AI', '量化', '交易'],
      isInternal: true,
      isFavorite: false,
      accessCount: 1250,
      likeCount: 89,
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
    {
      id: '2',
      name: '风险监控中心',
      description: '实时监控交易风险，提供全面的风险分析报告',
      url: 'https://risk.example.com',
      icon: 'Shield',
      category: 'risk',
      tags: ['风险', '监控', '实时'],
      isInternal: true,
      isFavorite: false,
      accessCount: 987,
      likeCount: 67,
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-14T14:20:00Z',
    },
    {
      id: '3',
      name: '数据分析平台',
      description: '强大的数据可视化和分析工具，支持多维度数据挖掘',
      url: 'https://analytics.example.com',
      icon: 'BarChart3',
      category: 'analytics',
      tags: ['数据', '可视化', '分析'],
      isInternal: true,
      isFavorite: false,
      accessCount: 1456,
      likeCount: 123,
      createdAt: '2024-01-13T09:15:00Z',
      updatedAt: '2024-01-13T09:15:00Z',
    },
    {
      id: '4',
      name: '客户关系管理',
      description: '全面的客户信息管理和服务追踪系统',
      url: 'https://crm.example.com',
      icon: 'Users',
      category: 'customer',
      tags: ['CRM', '客户', '服务'],
      isInternal: true,
      isFavorite: false,
      accessCount: 743,
      likeCount: 45,
      createdAt: '2024-01-12T16:45:00Z',
      updatedAt: '2024-01-12T16:45:00Z',
    },
    {
      id: '5',
      name: '财务管理系统',
      description: '集成化的财务管理和会计处理系统',
      url: 'https://finance.example.com',
      icon: 'Calculator',
      category: 'finance',
      tags: ['财务', '会计', '管理'],
      isInternal: true,
      isFavorite: false,
      accessCount: 892,
      likeCount: 78,
      createdAt: '2024-01-11T11:30:00Z',
      updatedAt: '2024-01-11T11:30:00Z',
    },
    {
      id: '6',
      name: '开发者工具箱',
      description: '包含代码管理、CI/CD、监控等开发工具',
      url: 'https://devtools.example.com',
      icon: 'Settings',
      category: 'tools',
      tags: ['开发', '工具', 'CI/CD'],
      isInternal: true,
      isFavorite: false,
      accessCount: 567,
      likeCount: 34,
      createdAt: '2024-01-10T13:20:00Z',
      updatedAt: '2024-01-10T13:20:00Z',
    },
    {
      id: '7',
      name: '期货交易系统',
      description: '专业的期货交易平台，支持多种期货品种',
      url: 'https://futures.example.com',
      icon: 'TrendingUp',
      category: 'trading',
      tags: ['期货', '交易', '专业'],
      isInternal: true,
      isFavorite: false,
      accessCount: 1123,
      likeCount: 91,
      createdAt: '2024-01-09T08:45:00Z',
      updatedAt: '2024-01-09T08:45:00Z',
    },
    {
      id: '8',
      name: '合规管理平台',
      description: '确保业务合规，提供全面的合规检查和报告',
      url: 'https://compliance.example.com',
      icon: 'Shield',
      category: 'risk',
      tags: ['合规', '检查', '报告'],
      isInternal: true,
      isFavorite: false,
      accessCount: 654,
      likeCount: 52,
      createdAt: '2024-01-08T15:10:00Z',
      updatedAt: '2024-01-08T15:10:00Z',
    },
    {
      id: '9',
      name: '市场数据中心',
      description: '实时市场数据Feed和历史数据查询',
      url: 'https://market.example.com',
      icon: 'BarChart3',
      category: 'analytics',
      tags: ['市场', '数据', '实时'],
      isInternal: true,
      isFavorite: false,
      accessCount: 2145,
      likeCount: 156,
      createdAt: '2024-01-07T12:30:00Z',
      updatedAt: '2024-01-07T12:30:00Z',
    },
    {
      id: '10',
      name: '客户服务门户',
      description: '为客户提供自助服务和在线支持',
      url: 'https://support.example.com',
      icon: 'Users',
      category: 'customer',
      tags: ['客户', '服务', '支持'],
      isInternal: false,
      isFavorite: false,
      accessCount: 834,
      likeCount: 67,
      createdAt: '2024-01-06T10:15:00Z',
      updatedAt: '2024-01-06T10:15:00Z',
    },
    {
      id: '11',
      name: '结算管理系统',
      description: '自动化的交易结算和清算处理',
      url: 'https://settlement.example.com',
      icon: 'Calculator',
      category: 'finance',
      tags: ['结算', '清算', '自动化'],
      isInternal: true,
      isFavorite: false,
      accessCount: 445,
      likeCount: 32,
      createdAt: '2024-01-05T14:50:00Z',
      updatedAt: '2024-01-05T14:50:00Z',
    },
    {
      id: '12',
      name: '系统监控平台',
      description: '实时监控系统健康状态和性能指标',
      url: 'https://monitoring.example.com',
      icon: 'Settings',
      category: 'tools',
      tags: ['监控', '性能', '运维'],
      isInternal: true,
      isFavorite: false,
      accessCount: 778,
      likeCount: 56,
      createdAt: '2024-01-04T09:25:00Z',
      updatedAt: '2024-01-04T09:25:00Z',
    },
  ] as NavItem[],
};