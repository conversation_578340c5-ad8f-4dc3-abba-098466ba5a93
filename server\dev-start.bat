@echo off
chcp 65001 >nul

echo 🚀 启动 次席管理平台 开发环境后端服务...

REM 进入脚本所在目录
cd /d "%~dp0"

REM 检查Node.js版本
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Node.js，请先安装 Node.js 16+ 版本
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js 版本: %NODE_VERSION%

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 安装开发依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

REM 创建开发环境数据目录
if not exist "data" mkdir data
if not exist "logs" mkdir logs

echo 📁 数据目录: %cd%\data
echo 📊 日志目录: %cd%\logs

REM 设置开发环境变量
set NODE_ENV=development
if "%PORT%"=="" set PORT=3001
set DEBUG=true

echo 🔧 开发环境配置:
echo    - 环境: %NODE_ENV%
echo    - 端口: %PORT%
echo    - 调试模式: %DEBUG%
echo    - 热重载: 启用

REM 检查端口是否被占用
netstat -an | find ":%PORT%" | find "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️ 端口 %PORT% 已被占用，正在尝试终止占用进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| find ":%PORT%" ^| find "LISTENING"') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 2 >nul
)

echo.
echo 🌟 启动开发服务器...
echo 📍 服务地址: http://localhost:%PORT%
echo 🔍 健康检查: http://localhost:%PORT%/health
echo 📚 API文档: http://localhost:%PORT%/api
echo.
echo 💡 开发提示:
echo    - 文件修改会自动重启服务器
echo    - 使用 Ctrl+C 停止服务器
echo    - 日志文件: logs\dev.log
echo.

REM 启动开发服务器
call npm run dev

pause
