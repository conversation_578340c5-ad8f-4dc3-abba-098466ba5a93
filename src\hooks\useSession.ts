import { useState, useEffect, useCallback } from 'react';
import { sessionManager } from '../utils/sessionManager';
import { useAppStore } from '../store/useAppStore';

interface UseSessionReturn {
  isSessionValid: boolean;
  showWarningModal: boolean;
  showExpiredModal: boolean;
  remainingTime: number;
  sessionInfo: { userId: string; remainingTime: number; loginTime: number } | null;
  extendSession: () => void;
  logout: () => void;
  closeWarningModal: () => void;
}

export const useSession = (): UseSessionReturn => {
  const [isSessionValid, setIsSessionValid] = useState(false);
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [showExpiredModal, setShowExpiredModal] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);
  const [sessionInfo, setSessionInfo] = useState<{ userId: string; remainingTime: number; loginTime: number } | null>(null);

  const { currentUser, setCurrentUser } = useAppStore();

  // 初始化会话
  const initializeSession = useCallback(() => {
    if (currentUser && !sessionManager.isSessionValid()) {
      // 如果有用户但会话无效，创建新会话
      sessionManager.createSession(currentUser.id);
    }
    
    const valid = sessionManager.isSessionValid();
    setIsSessionValid(valid);
    
    if (valid) {
      const info = sessionManager.getSessionInfo();
      setSessionInfo(info);
      setRemainingTime(info?.remainingTime || 0);
    }
  }, [currentUser]);

  // 会话过期处理
  const handleSessionExpired = useCallback(() => {
    console.log('Session expired, showing modal');
    setIsSessionValid(false);
    setShowExpiredModal(true);
    setShowWarningModal(false);
  }, []);

  // 会话警告处理
  const handleSessionWarning = useCallback((remainingMinutes: number) => {
    console.log('Session warning, remaining minutes:', remainingMinutes);
    setRemainingTime(remainingMinutes);
    setShowWarningModal(true);
  }, []);

  // 延长会话
  const extendSession = useCallback(() => {
    sessionManager.extendSession();
    setShowWarningModal(false);
    setIsSessionValid(true);
    
    const info = sessionManager.getSessionInfo();
    setSessionInfo(info);
    setRemainingTime(info?.remainingTime || 0);
    
    console.log('Session extended');
  }, []);

  // 登出
  const logout = useCallback(() => {
    sessionManager.destroySession();
    setCurrentUser(null);
    setIsSessionValid(false);
    setShowWarningModal(false);
    setShowExpiredModal(false);
    setSessionInfo(null);
    setRemainingTime(0);
    
    console.log('User logged out');
  }, [setCurrentUser]);

  // 关闭警告模态框
  const closeWarningModal = useCallback(() => {
    setShowWarningModal(false);
  }, []);

  // 设置会话管理器回调
  useEffect(() => {
    sessionManager.setSessionExpiredCallback(handleSessionExpired);
    sessionManager.setSessionWarningCallback(handleSessionWarning);

    return () => {
      sessionManager.stopActivityMonitoring();
    };
  }, [handleSessionExpired, handleSessionWarning]);

  // 初始化会话状态
  useEffect(() => {
    initializeSession();
  }, [initializeSession]);

  // 定期更新会话信息
  useEffect(() => {
    const interval = setInterval(() => {
      if (sessionManager.isSessionValid()) {
        const info = sessionManager.getSessionInfo();
        setSessionInfo(info);
        setRemainingTime(Math.ceil((info?.remainingTime || 0) / 60000)); // 转换为分钟
      }
    }, 60000); // 每分钟更新一次

    return () => clearInterval(interval);
  }, []);

  // 监听用户变化
  useEffect(() => {
    if (currentUser && !sessionManager.isSessionValid()) {
      sessionManager.createSession(currentUser.id);
      setIsSessionValid(true);
    } else if (!currentUser && sessionManager.isSessionValid()) {
      sessionManager.destroySession();
      setIsSessionValid(false);
    }
  }, [currentUser]);

  return {
    isSessionValid,
    showWarningModal,
    showExpiredModal,
    remainingTime,
    sessionInfo,
    extendSession,
    logout,
    closeWarningModal,
  };
};
