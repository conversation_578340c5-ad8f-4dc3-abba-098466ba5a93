# CJFCO Nav 部署指南

## 📋 部署配置

- **端口**: 5173
- **部署目录**: `/home/<USER>
- **Nginx 配置**: `nginx/cjfconav.conf`

## 🚀 快速部署

### 方法一：使用部署脚本（推荐）

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
sudo ./deploy.sh
```

### 方法二：手动部署

```bash
# 1. 构建项目
npm run build

# 2. 创建部署目录
sudo mkdir -p /home/<USER>

# 3. 复制文件
sudo cp -r dist/* /home/<USER>/

# 4. 设置权限
sudo chown -R www-data:www-data /home/<USER>
sudo chmod -R 755 /home/<USER>

# 5. 配置 Nginx
sudo cp nginx/cjfconav.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/cjfconav.conf /etc/nginx/sites-enabled/

# 6. 测试并重载配置
sudo nginx -t
sudo systemctl reload nginx
```

## ⚙️ Nginx 配置详解

### 主要配置项

```nginx
server {
    listen 5173;                    # 监听 5173 端口
    server_name localhost;          # 服务器名称
    root /home/<USER>
    index index.html;              # 默认首页
    
    # React Router 支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 性能优化配置

1. **静态资源缓存**
   - Assets 文件：1年缓存
   - 配置文件：1小时缓存
   - 图片资源：30天缓存

2. **Gzip 压缩**
   - 启用多种文件类型压缩
   - 压缩级别：6
   - 最小压缩文件：1024字节

3. **安全头配置**
   - X-Frame-Options
   - X-Content-Type-Options
   - X-XSS-Protection
   - Content-Security-Policy

## 📊 监控和维护

### 日志文件位置

```bash
# 访问日志
/var/log/nginx/cjfconav.access.log

# 错误日志
/var/log/nginx/cjfconav.error.log
```

### 常用命令

```bash
# 查看实时访问日志
sudo tail -f /var/log/nginx/cjfconav.access.log

# 查看错误日志
sudo tail -f /var/log/nginx/cjfconav.error.log

# 测试 Nginx 配置
sudo nginx -t

# 重新加载 Nginx 配置
sudo systemctl reload nginx

# 重启 Nginx 服务
sudo systemctl restart nginx

# 查看 Nginx 状态
sudo systemctl status nginx
```

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep 5173
   
   # 或使用 ss 命令
   sudo ss -tlnp | grep 5173
   ```

2. **权限问题**
   ```bash
   # 检查文件权限
   ls -la /home/<USER>
   
   # 重新设置权限
   sudo chown -R www-data:www-data /home/<USER>
   sudo chmod -R 755 /home/<USER>
   ```

3. **配置文件错误**
   ```bash
   # 测试配置
   sudo nginx -t
   
   # 查看详细错误
   sudo nginx -T
   ```

### 验证部署

```bash
# 检查服务是否运行
curl -I http://localhost:5173

# 检查配置文件是否可访问
curl http://localhost:5173/config/app.config.json

# 检查静态资源
curl -I http://localhost:5173/assets/
```

## 🔄 更新部署

### 更新应用

```bash
# 1. 构建新版本
npm run build

# 2. 备份当前版本（可选）
sudo cp -r /home/<USER>/home/<USER>

# 3. 更新文件
sudo cp -r dist/* /home/<USER>/

# 4. 重新加载 Nginx（如果配置有变化）
sudo systemctl reload nginx
```

### 回滚版本

```bash
# 恢复备份
sudo rm -rf /home/<USER>
sudo mv /home/<USER>/home/<USER>
sudo systemctl reload nginx
```

## 🔒 安全建议

1. **防火墙配置**
   ```bash
   # 只允许特定端口
   sudo ufw allow 5173
   ```

2. **定期更新**
   - 定期更新 Nginx
   - 定期更新系统安全补丁

3. **日志监控**
   - 设置日志轮转
   - 监控异常访问

## 📈 性能监控

### 监控指标

- 响应时间
- 并发连接数
- 错误率
- 资源使用率

### 监控命令

```bash
# 查看 Nginx 进程
ps aux | grep nginx

# 查看连接状态
sudo netstat -an | grep 5173

# 查看系统资源
htop
```

部署完成后，您的应用将在 `http://localhost:5173` 上运行！
