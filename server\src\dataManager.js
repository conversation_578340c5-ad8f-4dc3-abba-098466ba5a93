import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 数据管理器 - 内存缓存 + 文件持久化
 */
export class DataManager {
  constructor() {
    this.dataPath = path.join(__dirname, '../data');
    this.navItemsFile = path.join(this.dataPath, 'navItems.json');
    this.categoriesFile = path.join(this.dataPath, 'categories.json');
    this.statsFile = path.join(this.dataPath, 'stats.json');
    this.configFile = path.join(this.dataPath, 'config.json');
    
    // 内存缓存
    this.cache = {
      navItems: [],
      categories: [],
      stats: {},
      config: {},
      lastSaved: null
    };
    
    // 自动保存间隔（5分钟）
    this.autoSaveInterval = 5 * 60 * 1000;
    this.autoSaveTimer = null;
  }

  /**
   * 初始化数据管理器
   */
  async initialize() {
    try {
      // 确保数据目录存在
      await fs.mkdir(this.dataPath, { recursive: true });
      
      // 加载数据到内存
      await this.loadData();
      
      // 启动自动保存
      this.startAutoSave();
      
      console.log('✅ 数据管理器初始化成功');
    } catch (error) {
      console.error('❌ 数据管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 从文件加载数据到内存
   */
  async loadData() {
    try {
      // 加载导航项目
      this.cache.navItems = await this.loadJsonFile(this.navItemsFile, this.getDefaultNavItems());
      
      // 加载分类
      this.cache.categories = await this.loadJsonFile(this.categoriesFile, this.getDefaultCategories());
      
      // 加载统计数据
      this.cache.stats = await this.loadJsonFile(this.statsFile, this.getDefaultStats());
      
      // 加载配置
      this.cache.config = await this.loadJsonFile(this.configFile, this.getDefaultConfig());
      
      console.log(`📊 数据加载完成: ${this.cache.navItems.length} 个应用, ${this.cache.categories.length} 个分类`);
    } catch (error) {
      console.error('❌ 数据加载失败:', error);
      throw error;
    }
  }

  /**
   * 加载JSON文件
   */
  async loadJsonFile(filePath, defaultData) {
    try {
      const data = await fs.readFile(filePath, 'utf8');

      // 检查文件是否为空或只包含空白字符
      if (!data || data.trim() === '') {
        console.log(`⚠️  文件 ${filePath} 为空，使用默认数据`);
        await this.saveJsonFile(filePath, defaultData);
        return defaultData;
      }

      try {
        return JSON.parse(data);
      } catch (parseError) {
        console.log(`⚠️  文件 ${filePath} JSON格式错误，使用默认数据`);
        console.log(`错误内容: "${data.substring(0, 100)}..."`);
        await this.saveJsonFile(filePath, defaultData);
        return defaultData;
      }
    } catch (error) {
      if (error.code === 'ENOENT') {
        // 文件不存在，使用默认数据并保存
        console.log(`📁 文件 ${filePath} 不存在，创建默认数据`);
        await this.saveJsonFile(filePath, defaultData);
        return defaultData;
      }
      throw error;
    }
  }

  /**
   * 保存JSON文件
   */
  async saveJsonFile(filePath, data) {
    const jsonData = JSON.stringify(data, null, 2);
    await fs.writeFile(filePath, jsonData, 'utf8');
  }

  /**
   * 保存所有数据到文件
   */
  async saveData() {
    try {
      await Promise.all([
        this.saveJsonFile(this.navItemsFile, this.cache.navItems),
        this.saveJsonFile(this.categoriesFile, this.cache.categories),
        this.saveJsonFile(this.statsFile, this.cache.stats),
        this.saveJsonFile(this.configFile, this.cache.config)
      ]);
      
      this.cache.lastSaved = new Date().toISOString();
      console.log('💾 数据保存成功');
    } catch (error) {
      console.error('❌ 数据保存失败:', error);
      throw error;
    }
  }

  /**
   * 启动自动保存
   */
  startAutoSave() {
    this.autoSaveTimer = setInterval(async () => {
      try {
        await this.saveData();
      } catch (error) {
        console.error('❌ 自动保存失败:', error);
      }
    }, this.autoSaveInterval);
  }

  /**
   * 停止自动保存
   */
  stopAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }

  // ==================== 数据操作方法 ====================

  /**
   * 获取所有导航项目
   */
  getNavItems() {
    return [...this.cache.navItems];
  }

  /**
   * 添加导航项目
   */
  addNavItem(item) {
    const newItem = {
      ...item,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      accessCount: 0
    };
    
    this.cache.navItems.push(newItem);
    return newItem;
  }

  /**
   * 更新导航项目
   */
  updateNavItem(id, updates) {
    const index = this.cache.navItems.findIndex(item => item.id === id);
    if (index === -1) {
      throw new Error('导航项目不存在');
    }
    
    this.cache.navItems[index] = {
      ...this.cache.navItems[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    return this.cache.navItems[index];
  }

  /**
   * 删除导航项目
   */
  deleteNavItem(id) {
    const index = this.cache.navItems.findIndex(item => item.id === id);
    if (index === -1) {
      throw new Error('导航项目不存在');
    }
    
    const deletedItem = this.cache.navItems.splice(index, 1)[0];
    return deletedItem;
  }

  /**
   * 增加访问次数
   */
  incrementAccessCount(id) {
    const item = this.cache.navItems.find(item => item.id === id);
    if (item) {
      item.accessCount = (item.accessCount || 0) + 1;
      item.lastAccessed = new Date().toISOString();
      
      // 更新全局统计
      this.cache.stats.totalViews = (this.cache.stats.totalViews || 0) + 1;
      
      return item;
    }
    throw new Error('导航项目不存在');
  }

  /**
   * 获取所有分类
   */
  getCategories() {
    return [...this.cache.categories];
  }

  /**
   * 添加分类
   */
  addCategory(category) {
    const newCategory = {
      ...category,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      itemCount: 0
    };
    
    this.cache.categories.push(newCategory);
    return newCategory;
  }

  /**
   * 更新分类
   */
  updateCategory(id, updates) {
    const index = this.cache.categories.findIndex(cat => cat.id === id);
    if (index === -1) {
      throw new Error('分类不存在');
    }
    
    this.cache.categories[index] = {
      ...this.cache.categories[index],
      ...updates
    };
    
    return this.cache.categories[index];
  }

  /**
   * 删除分类
   */
  deleteCategory(id) {
    const index = this.cache.categories.findIndex(cat => cat.id === id);
    if (index === -1) {
      throw new Error('分类不存在');
    }
    
    const category = this.cache.categories[index];
    if (category.isSystem) {
      throw new Error('系统分类不能删除');
    }
    
    const deletedCategory = this.cache.categories.splice(index, 1)[0];
    return deletedCategory;
  }

  /**
   * 获取统计数据
   */
  getStats() {
    // 实时计算统计数据
    const totalItems = this.cache.navItems.length;
    const totalViews = this.cache.navItems.reduce((sum, item) => sum + (item.accessCount || 0), 0);
    const totalCategories = this.cache.categories.length;
    
    return {
      ...this.cache.stats,
      totalItems,
      totalViews,
      totalCategories,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * 获取配置
   */
  getConfig() {
    return { ...this.cache.config };
  }

  /**
   * 更新配置
   */
  updateConfig(updates) {
    this.cache.config = {
      ...this.cache.config,
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    return this.cache.config;
  }

  // ==================== 工具方法 ====================

  /**
   * 生成唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 获取存储路径
   */
  getStoragePath() {
    return this.dataPath;
  }

  /**
   * 获取默认导航项目
   */
  getDefaultNavItems() {
    return [];
  }

  /**
   * 获取默认分类
   */
  getDefaultCategories() {
    return [
      {
        id: 'all',
        name: '全部应用',
        color: '#6B7280',
        icon: 'Grid',
        description: '显示所有应用',
        itemCount: 0,
        isSystem: true
      },
      {
        id: 'favorites',
        name: '收藏夹',
        color: '#F59E0B',
        icon: 'Star',
        description: '收藏的应用',
        itemCount: 0,
        isSystem: true
      }
    ];
  }

  /**
   * 获取默认统计数据
   */
  getDefaultStats() {
    return {
      totalItems: 0,
      totalViews: 0,
      totalCategories: 0,
      createdAt: new Date().toISOString()
    };
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return {
      siteName: 'CJFCO Nav',
      siteDescription: '长江期货导航系统',
      theme: {
        primaryColor: '#3B82F6',
        secondaryColor: '#8B5CF6',
        darkMode: false
      },
      features: {
        allowUserRegistration: false,
        enableComments: false,
        enableRating: true
      },
      createdAt: new Date().toISOString()
    };
  }
}
