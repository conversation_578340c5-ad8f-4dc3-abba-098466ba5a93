import React from 'react';
import { useAppStore } from '../store/useAppStore';
import { 
  TrendingUp, 
  Shield, 
  BarChart3, 
  Users, 
  Calculator, 
  Settings,
  Grid,
  Star,
  Plus
} from 'lucide-react';

const iconMap = {
  TrendingUp,
  Shield,
  BarChart3,
  Users,
  Calculator,
  Settings,
  Grid,
  Star,
  Plus,
};

export const Sidebar: React.FC = () => {
  const {
    categories,
    selectedCategory,
    setSelectedCategory,
    showFavorites,
    setShowFavorites,
    isAdminMode,
    getCategoryStats,
    getStats,
  } = useAppStore();

  const categoryStats = getCategoryStats();
  const stats = getStats();

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-screen overflow-y-auto">
      <div className="p-6">
        {/* Stats Overview */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">概览</h3>
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalItems}</div>
              <div className="text-xs text-blue-600">应用总数</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.totalViews}</div>
              <div className="text-xs text-green-600">访问次数</div>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">分类</h3>
          <div className="space-y-1">
            {/* All Categories */}
            <button
              onClick={() => {
                setSelectedCategory('all');
                setShowFavorites(false);
              }}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                selectedCategory === 'all' && !showFavorites
                  ? 'bg-blue-50 text-blue-600 border border-blue-200'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Grid className="w-4 h-4" />
                <span>全部应用</span>
              </div>
              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                {stats.totalItems}
              </span>
            </button>

            {/* Favorites */}
            <button
              onClick={() => setShowFavorites(true)}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                showFavorites
                  ? 'bg-yellow-50 text-yellow-600 border border-yellow-200'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4" />
                <span>收藏</span>
              </div>
              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                {stats.totalFavorites}
              </span>
            </button>

            {/* Category List */}
            {categoryStats.map((category) => {
              const IconComponent = iconMap[category.icon as keyof typeof iconMap] || Settings;
              return (
                <button
                  key={category.id}
                  onClick={() => {
                    setSelectedCategory(category.id);
                    setShowFavorites(false);
                  }}
                  className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                    selectedCategory === category.id && !showFavorites
                      ? 'bg-blue-50 text-blue-600 border border-blue-200'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <IconComponent className="w-4 h-4" style={{ color: category.color }} />
                    <span>{category.name}</span>
                  </div>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                    {category.itemCount}
                  </span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Admin Actions */}
        {isAdminMode && (
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">管理操作</h3>
            <div className="space-y-2">
              <button className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-600 hover:bg-gray-50 transition-colors">
                <Plus className="w-4 h-4" />
                <span>添加应用</span>
              </button>
              <button className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-600 hover:bg-gray-50 transition-colors">
                <Plus className="w-4 h-4" />
                <span>添加分类</span>
              </button>
            </div>
          </div>
        )}

        {/* Popular Apps */}
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-3">热门应用</h3>
          <div className="space-y-2">
            {stats.mostPopular.slice(0, 3).map((item) => (
              <div
                key={item.id}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
              >
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs font-medium">
                    {item.name.charAt(0)}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {item.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {item.accessCount} 次访问
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};