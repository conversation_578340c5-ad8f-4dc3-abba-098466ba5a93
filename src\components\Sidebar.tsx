import React, { useState } from 'react';
import { useAppStore } from '../store/useAppStore';
import { accessTracker } from '../utils/accessTracker';
import { getAppInfo } from '../utils/version';
import { AddNavItemModal } from './AddNavItemModal';
import { AddCategoryModal } from './AddCategoryModal';
import { EditCategoryModal } from './EditCategoryModal';
import { Category } from '../types';
import {
  TrendingUp,
  Shield,
  BarChart3,
  Users,
  Calculator,
  Settings,
  Grid,
  Star,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal
} from 'lucide-react';

const iconMap = {
  TrendingUp,
  Shield,
  BarChart3,
  Users,
  Calculator,
  Settings,
  Grid,
  Star,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
};

export const Sidebar: React.FC = () => {
  const {
    categories,
    selectedCategory,
    setSelectedCategory,
    showFavorites,
    setShowFavorites,
    isAdminMode,
    getCategoryStats,
    getStats,
    incrementAccessCount,
    deleteCategory,
  } = useAppStore();

  const appInfo = getAppInfo();

  const [showAddItemModal, setShowAddItemModal] = useState(false);
  const [showAddCategoryModal, setShowAddCategoryModal] = useState(false);
  const [showEditCategoryModal, setShowEditCategoryModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  const categoryStats = getCategoryStats();
  const stats = getStats();

  // 处理编辑分类
  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setShowEditCategoryModal(true);
  };

  // 处理删除分类
  const handleDeleteCategory = (category: Category) => {
    if (category.isSystem) {
      alert('系统默认分类不能删除');
      return;
    }

    if (category.itemCount > 0) {
      alert(`该分类下还有 ${category.itemCount} 个应用，请先移动或删除这些应用后再删除分类`);
      return;
    }

    if (window.confirm(`确定要删除分类"${category.name}"吗？此操作无法撤销。`)) {
      deleteCategory(category.id);
      // 如果当前选中的是被删除的分类，切换到全部应用
      if (selectedCategory === category.id) {
        setSelectedCategory('all');
        setShowFavorites(false);
      }
    }
  };

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-screen flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
        {/* Stats Overview */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">概览</h3>
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalItems}</div>
              <div className="text-xs text-blue-600">应用总数</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.totalViews}</div>
              <div className="text-xs text-green-600">访问次数</div>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">分类</h3>
          <div className="space-y-1">
            {/* All Categories */}
            <button
              onClick={() => {
                setSelectedCategory('all');
                setShowFavorites(false);
              }}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                selectedCategory === 'all' && !showFavorites
                  ? 'bg-blue-50 text-blue-600 border border-blue-200'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Grid className="w-4 h-4" />
                <span>全部应用</span>
              </div>
              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                {stats.totalItems}
              </span>
            </button>

            {/* Favorites */}
            <button
              onClick={() => setShowFavorites(true)}
              className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                showFavorites
                  ? 'bg-yellow-50 text-yellow-600 border border-yellow-200'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4" />
                <span>收藏</span>
              </div>
              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                {stats.totalFavorites}
              </span>
            </button>

            {/* Category List */}
            {categoryStats.map((category) => {
              const IconComponent = iconMap[category.icon as keyof typeof iconMap] || Settings;
              return (
                <div
                  key={category.id}
                  className={`group relative flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors ${
                    selectedCategory === category.id && !showFavorites
                      ? 'bg-blue-50 text-blue-600 border border-blue-200'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  <button
                    onClick={() => {
                      setSelectedCategory(category.id);
                      setShowFavorites(false);
                    }}
                    className="flex-1 flex items-center space-x-2 text-left"
                  >
                    <IconComponent className="w-4 h-4" style={{ color: category.color }} />
                    <span>{category.name}</span>
                  </button>

                  <div className="flex items-center space-x-1">
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                      {category.itemCount}
                    </span>

                    {/* 管理员模式下显示编辑和删除按钮 */}
                    {isAdminMode && !category.isSystem && (
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditCategory(category);
                          }}
                          className="p-1 rounded hover:bg-gray-200 transition-colors"
                          title="编辑分类"
                        >
                          <Edit className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteCategory(category);
                          }}
                          className="p-1 rounded hover:bg-red-200 hover:text-red-600 transition-colors"
                          title="删除分类"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Admin Actions */}
        {isAdminMode && (
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">管理操作</h3>
            <div className="space-y-2">
              <button
                onClick={() => setShowAddItemModal(true)}
                className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-600 hover:bg-gray-50 transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>添加应用</span>
              </button>
              <button
                onClick={() => setShowAddCategoryModal(true)}
                className="w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-600 hover:bg-gray-50 transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>添加分类</span>
              </button>
            </div>
          </div>
        )}

        {/* Popular Apps */}
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-3">热门应用</h3>
          <div className="space-y-2">
            {stats.mostPopular.slice(0, 3).map((item) => (
              <div
                key={item.id}
                onClick={() => {
                  // 使用访问跟踪器打开链接
                  const success = accessTracker.trackAccess(item.id, item.url, () => {
                    // 只有成功打开页面才增加访问计数
                    incrementAccessCount(item.id);
                  });

                  if (!success) {
                    // 如果弹窗被阻止，提示用户
                    alert('弹窗被浏览器阻止，请允许弹窗或手动打开链接：' + item.url);
                  }
                }}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
              >
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs font-medium">
                    {item.name.charAt(0)}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {item.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {item.accessCount} 次访问
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        </div>
      </div>

      {/* 版本信息 - 固定在底部 */}
      <div className="flex-shrink-0 p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-center">
          <div className="text-xs text-gray-400 font-mono font-semibold">
            版本：{appInfo.version}
          </div>
          <div className="text-xs text-gray-400 mt-1">
            {appInfo.copyright}
          </div>
        </div>
      </div>

      {/* 模态框 */}
      <AddNavItemModal
        isOpen={showAddItemModal}
        onClose={() => setShowAddItemModal(false)}
      />

      <AddCategoryModal
        isOpen={showAddCategoryModal}
        onClose={() => setShowAddCategoryModal(false)}
      />

      <EditCategoryModal
        isOpen={showEditCategoryModal}
        category={editingCategory}
        onClose={() => {
          setShowEditCategoryModal(false);
          setEditingCategory(null);
        }}
      />
    </div>
  );
};