{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/uuid": "^10.0.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^24.0.12", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}, "browserslist": ["defaults", "not IE 11", "not op_mini all", "Chrome >= 80", "Firefox >= 78", "Safari >= 14", "Edge >= 80"]}