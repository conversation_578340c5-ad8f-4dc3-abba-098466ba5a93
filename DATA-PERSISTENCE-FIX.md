# 数据持久化修复说明

## 问题描述

之前的版本中，每次重启服务后数据都会恢复到初始状态，用户添加的应用、分类和设置都会丢失。

## 问题原因

1. **初始状态覆盖**：在 `useAppStore.ts` 中，初始状态总是使用 `mockData`，这会覆盖 Zustand persist 中间件恢复的数据
2. **时序问题**：应用启动时，persist 中间件还没有完成数据恢复，就被初始状态覆盖了

## 修复方案

### 1. 修改初始状态

**修改前**：
```typescript
// Initial state
items: mockData.items,
categories: mockData.categories,
currentUser: mockData.currentUser,
```

**修改后**：
```typescript
// Initial state - 只在没有持久化数据时使用 mockData
items: [],
categories: [],
currentUser: null,
```

### 2. 添加智能初始化

添加了 `initializeWithMockData()` 方法，只在数据为空时才使用模拟数据：

```typescript
initializeWithMockData: () => {
  const { items, categories, currentUser } = get();
  // 只在数据为空时初始化
  if (items.length === 0 && categories.length === 0 && !currentUser) {
    set({
      items: mockData.items,
      categories: mockData.categories,
      currentUser: mockData.currentUser,
    });
  }
},
```

### 3. 延迟初始化

在 `main.tsx` 中添加延迟初始化，确保 persist 中间件先恢复数据：

```typescript
// 延迟初始化，确保 Zustand persist 已经恢复数据
setTimeout(initializeApp, 100);
```

## 数据存储机制

### 存储位置
- **主要数据**：`localStorage['fintech-nav-storage']`
- **会话数据**：`localStorage['fintech-nav-session']`

### 持久化内容
```typescript
partialize: (state) => ({
  items: state.items,           // 应用数据
  categories: state.categories, // 分类数据
  currentUser: state.currentUser, // 用户数据
  viewMode: state.viewMode,     // 视图模式
}),
```

### 不持久化内容
- `searchQuery` - 搜索查询（临时状态）
- `selectedCategory` - 当前选中分类（会话状态）
- `showFavorites` - 收藏筛选状态（临时状态）
- `isAdminMode` - 管理员模式（安全考虑）

## 调试工具

添加了 `debugStorage` 调试工具，在开发环境中可以使用：

### 浏览器控制台命令

```javascript
// 查看所有存储数据
debugStorage.viewAll()

// 检查数据完整性
debugStorage.checkIntegrity()

// 清除所有数据
debugStorage.clearAll()

// 导出数据备份
debugStorage.exportData()

// 重置为默认数据
debugStorage.resetToDefault()
```

## 验证修复

### 测试步骤

1. **启动应用**：`npm run dev`
2. **添加测试数据**：
   - 进入管理模式
   - 添加一个新应用
   - 添加一个新分类
   - 收藏几个应用
3. **重启服务**：停止并重新运行 `npm run dev`
4. **验证数据**：检查之前添加的数据是否还在

### 浏览器验证

在浏览器控制台运行：
```javascript
// 查看存储的数据
debugStorage.viewAll()

// 检查数据完整性
debugStorage.checkIntegrity()
```

## 数据恢复

如果数据丢失，可以：

1. **检查存储状态**：
   ```javascript
   debugStorage.viewAll()
   ```

2. **重置为默认数据**：
   ```javascript
   debugStorage.resetToDefault()
   ```

3. **手动清除缓存**：
   - 打开浏览器开发者工具
   - Application → Storage → Local Storage
   - 删除相关键值对

## 注意事项

1. **浏览器兼容性**：使用 localStorage，需要现代浏览器支持
2. **存储限制**：localStorage 通常有 5-10MB 的存储限制
3. **隐私模式**：在浏览器隐私模式下，localStorage 可能不持久化
4. **跨域问题**：不同域名下的数据是隔离的

## 未来改进

1. **云端同步**：可以考虑添加云端数据同步功能
2. **数据压缩**：对于大量数据，可以考虑压缩存储
3. **版本管理**：添加数据版本控制，支持数据迁移
4. **备份恢复**：自动备份和恢复功能
