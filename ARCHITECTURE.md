# CJFCO Nav 系统架构文档

## 🏗️ 整体架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                        用户访问层                                │
├─────────────────────────────────────────────────────────────────┤
│  浏览器客户端 A    │  浏览器客户端 B    │  浏览器客户端 C      │
│  (Chrome/Edge)     │  (Firefox)         │  (Safari)            │
└─────────┬───────────┴─────────┬─────────┴─────────┬─────────────┘
          │                     │                   │
          ▼                     ▼                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                      负载均衡层 (可选)                           │
│                    Nginx / Apache                               │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                       Web 服务层                                │
│  ┌─────────────────┐                    ┌─────────────────┐     │
│  │   静态文件服务   │                    │   API 代理      │     │
│  │  (前端资源)     │                    │  (/api/* → )   │     │
│  │  Nginx/IIS      │                    │   后端服务      │     │
│  └─────────────────┘                    └─────────────────┘     │
└─────────────────────────┬───────────────────────┬───────────────┘
                          │                       │
                          ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                      应用服务层                                  │
│  ┌─────────────────┐                    ┌─────────────────┐     │
│  │   React 前端    │◄──── HTTP API ────►│  Node.js 后端   │     │
│  │                 │                    │                 │     │
│  │  • 用户界面     │                    │  • RESTful API  │     │
│  │  • 状态管理     │                    │  • 业务逻辑     │     │
│  │  • 本地缓存     │                    │  • 数据验证     │     │
│  │  • 离线支持     │                    │  • 访问统计     │     │
│  └─────────────────┘                    └─────────────────┘     │
└─────────────────────────────────────────────────┬───────────────┘
                                                  │
                                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                      数据存储层                                  │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐│
│  │   内存缓存      │    │   文件存储      │    │   备份存储      ││
│  │                 │    │                 │    │                 ││
│  │  • 高速访问     │    │  • JSON 文件    │    │  • 定时备份     ││
│  │  • 实时数据     │    │  • 持久化       │    │  • 版本控制     ││
│  │  • 自动同步     │    │  • 事务安全     │    │  • 灾难恢复     ││
│  └─────────────────┘    └─────────────────┘    └─────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 技术栈

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Zustand
- **UI 组件**: Tailwind CSS + Lucide Icons
- **HTTP 客户端**: Fetch API
- **本地存储**: localStorage (备份)

### 后端技术栈
- **运行时**: Node.js 16+
- **框架**: Express.js
- **语言**: JavaScript (ES Modules)
- **数据存储**: JSON 文件 + 内存缓存
- **安全**: Helmet + CORS + Rate Limiting
- **压缩**: Gzip

### 基础设施
- **Web 服务器**: Nginx (推荐) / Apache / IIS
- **进程管理**: PM2 (生产环境)
- **反向代理**: Nginx
- **日志管理**: Winston (可选)
- **监控**: 自定义健康检查

## 📊 数据流架构

### 数据流向图
```
用户操作 → 前端组件 → API 客户端 → HTTP 请求 → 后端 API → 数据管理器 → 内存缓存
    ↑                                                                      ↓
本地存储 ←── 离线缓存 ←── 数据同步 ←── API 响应 ←── JSON 响应 ←── 文件存储
```

### 数据同步机制
1. **在线模式**: 直接与服务器通信，实时同步
2. **离线模式**: 操作存储在本地队列，服务器恢复后自动同步
3. **混合模式**: 优先使用服务器数据，本地数据作为备份

## 🗄️ 数据存储设计

### 存储层次
```
┌─────────────────────────────────────────────────────────────┐
│                    第一层：内存缓存                          │
│  • 所有数据加载到内存                                       │
│  • 提供毫秒级响应                                           │
│  • 支持高并发访问                                           │
│  • 自动数据同步                                             │
└─────────────────────┬───────────────────────────────────────┘
                      │ 定时保存 (5分钟)
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                    第二层：文件存储                          │
│  • JSON 格式存储                                            │
│  • 原子性写入                                               │
│  • 数据持久化                                               │
│  • 易于备份和迁移                                           │
└─────────────────────┬───────────────────────────────────────┘
                      │ 定时备份 (每日)
                      ▼
┌─────────────────────────────────────────────────────────────┐
│                    第三层：备份存储                          │
│  • 历史版本保存                                             │
│  • 灾难恢复                                                 │
│  • 数据审计                                                 │
│  • 版本回滚                                                 │
└─────────────────────────────────────────────────────────────┘
```

### 数据文件结构
```
server/data/
├── navItems.json      # 导航项目数据
├── categories.json    # 分类数据
├── stats.json         # 统计数据
├── config.json        # 系统配置
└── backups/           # 备份目录
    ├── 20240101_020000/
    ├── 20240102_020000/
    └── ...
```

## 🔄 API 设计

### RESTful API 规范
```
GET    /api/nav-items           # 获取所有导航项目
POST   /api/nav-items           # 创建导航项目
PUT    /api/nav-items/:id       # 更新导航项目
DELETE /api/nav-items/:id       # 删除导航项目
POST   /api/nav-items/:id/access # 增加访问次数

GET    /api/categories          # 获取所有分类
POST   /api/categories          # 创建分类
PUT    /api/categories/:id      # 更新分类
DELETE /api/categories/:id      # 删除分类

GET    /api/stats               # 获取统计数据
GET    /api/config              # 获取配置
PUT    /api/config              # 更新配置

POST   /api/save                # 手动保存数据
GET    /api/status              # 获取服务器状态
GET    /health                  # 健康检查
```

### API 响应格式
```json
{
  "success": true,
  "data": { ... },
  "total": 100,
  "error": null
}
```

## 🔒 安全架构

### 安全层次
1. **网络层安全**
   - HTTPS 加密传输
   - 防火墙配置
   - IP 白名单 (可选)

2. **应用层安全**
   - CORS 跨域控制
   - 请求频率限制
   - 输入数据验证
   - XSS 防护头

3. **数据层安全**
   - 文件权限控制
   - 数据备份加密
   - 访问日志记录

### 安全配置示例
```javascript
// CORS 配置
app.use(cors({
  origin: ['http://localhost:5173', 'https://your-domain.com'],
  credentials: true
}));

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000 // 每IP最多1000次请求
});

// 安全头配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
```

## 📈 性能优化

### 前端性能优化
1. **代码分割**: 按路由和组件分割
2. **懒加载**: 图片和组件懒加载
3. **缓存策略**: 静态资源长期缓存
4. **压缩优化**: Gzip 压缩
5. **CDN 加速**: 静态资源 CDN 分发

### 后端性能优化
1. **内存缓存**: 所有数据内存存储
2. **连接池**: HTTP 连接复用
3. **压缩中间件**: 响应数据压缩
4. **异步处理**: 非阻塞 I/O 操作
5. **集群部署**: 多进程负载均衡

### 数据库性能优化
1. **批量操作**: 减少文件 I/O 次数
2. **原子写入**: 保证数据一致性
3. **定时保存**: 避免频繁磁盘写入
4. **索引优化**: 内存数据结构优化

## 🔄 部署架构

### 单机部署
```
┌─────────────────────────────────────┐
│            单台服务器                │
│  ┌─────────────┐  ┌─────────────┐   │
│  │    Nginx    │  │  Node.js    │   │
│  │   (前端)    │  │   (后端)    │   │
│  │  Port 5173  │  │  Port 3001  │   │
│  └─────────────┘  └─────────────┘   │
│         │               │           │
│         └───────┬───────┘           │
│                 │                   │
│         ┌─────────────┐             │
│         │  文件存储   │             │
│         │    JSON     │             │
│         └─────────────┘             │
└─────────────────────────────────────┘
```

### 集群部署 (可扩展)
```
┌─────────────────────────────────────────────────────────────┐
│                      负载均衡器                              │
│                    Nginx / HAProxy                          │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│   服务器 1   │ │   服务器 2   │ │   服务器 3   │
│             │ │             │ │             │
│ Node.js API │ │ Node.js API │ │ Node.js API │
│             │ │             │ │             │
└─────────────┘ └─────────────┘ └─────────────┘
        │             │             │
        └─────────────┼─────────────┘
                      │
                      ▼
        ┌─────────────────────────────┐
        │        共享存储             │
        │     NFS / 分布式文件系统     │
        └─────────────────────────────┘
```

## 🔍 监控和运维

### 监控指标
1. **系统指标**
   - CPU 使用率
   - 内存使用率
   - 磁盘空间
   - 网络流量

2. **应用指标**
   - 响应时间
   - 请求量 (QPS)
   - 错误率
   - 并发连接数

3. **业务指标**
   - 用户访问量
   - 应用点击量
   - 数据增长量
   - 功能使用率

### 日志管理
```
logs/
├── access.log         # 访问日志
├── error.log          # 错误日志
├── server.log         # 服务器日志
└── business.log       # 业务日志
```

### 备份策略
1. **实时备份**: 内存到文件的实时同步
2. **定时备份**: 每日自动备份到备份目录
3. **异地备份**: 定期同步到远程存储
4. **版本管理**: 保留最近 30 天的备份版本

这个架构设计确保了系统的高可用性、高性能和数据一致性，同时支持水平扩展和灾难恢复。
