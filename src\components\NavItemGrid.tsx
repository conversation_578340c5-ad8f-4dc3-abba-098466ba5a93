import React, { useState } from 'react';
import { NavItemCard } from './NavItemCard';
import { EditNavItemModal } from './EditNavItemModal';
import { useApiStore } from '../store/useApiStore';
import { NavItem } from '../types';
import { Inbox, Search } from 'lucide-react';

export const NavItemGrid: React.FC = () => {
  const { getFilteredItems, viewMode, searchQuery, showFavorites, selectedCategory } = useApiStore();

  const [editingItem, setEditingItem] = useState<NavItem | null>(null);
  const filteredItems = getFilteredItems();

  if (filteredItems.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          {searchQuery ? (
            <Search className="w-8 h-8 text-gray-400" />
          ) : (
            <Inbox className="w-8 h-8 text-gray-400" />
          )}
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {searchQuery ? '未找到相关应用' : '暂无应用'}
        </h3>
        <p className="text-gray-500 text-center max-w-md">
          {searchQuery 
            ? `没有找到与 "${searchQuery}" 相关的应用，请尝试其他关键词`
            : showFavorites 
              ? '您还没有收藏任何应用'
              : '该分类下暂无应用'
          }
        </p>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {showFavorites ? '收藏的应用' : selectedCategory === 'all' ? '所有应用' : '分类应用'}
        </h2>
        <p className="text-gray-600">
          {searchQuery && `搜索 "${searchQuery}" 的结果: `}
          找到 {filteredItems.length} 个应用
        </p>
      </div>

      {/* Grid */}
      <div className={
        viewMode === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6'
          : 'space-y-4'
      }>
        {filteredItems.map((item) => (
          <NavItemCard
            key={item.id}
            item={item}
            viewMode={viewMode}
            onEdit={() => setEditingItem(item)}
          />
        ))}
      </div>

      {/* 编辑模态框 */}
      <EditNavItemModal
        isOpen={!!editingItem}
        item={editingItem}
        onClose={() => setEditingItem(null)}
      />
    </div>
  );
};