import React, { useState, useEffect } from 'react';
import { useApiStore } from '../store/useApiStore';
import { Category } from '../types';
import { 
  X, 
  Save, 
  Type, 
  FileText, 
  Palette,
  TrendingUp,
  Shield,
  BarChart3,
  Users,
  Calculator,
  Settings,
  Globe,
  Building,
  Folder,
  Tag
} from 'lucide-react';

const iconOptions = [
  { value: 'TrendingUp', label: '趋势', icon: TrendingUp },
  { value: 'Shield', label: '安全', icon: Shield },
  { value: 'BarChart3', label: '图表', icon: BarChart3 },
  { value: 'Users', label: '用户', icon: Users },
  { value: 'Calculator', label: '计算器', icon: Calculator },
  { value: 'Settings', label: '设置', icon: Settings },
  { value: 'Globe', label: '全球', icon: Globe },
  { value: 'Building', label: '建筑', icon: Building },
  { value: 'Folder', label: '文件夹', icon: Folder },
  { value: 'Tag', label: '标签', icon: Tag },
];

const colorOptions = [
  '#1890ff', '#f5222d', '#52c41a', '#722ed1', '#fa8c16', '#13c2c2',
  '#eb2f96', '#faad14', '#a0d911', '#1890ff', '#722ed1', '#fa541c',
];

interface EditCategoryModalProps {
  isOpen: boolean;
  category: Category | null;
  onClose: () => void;
}

export const EditCategoryModal: React.FC<EditCategoryModalProps> = ({
  isOpen,
  category,
  onClose,
}) => {
  const { updateCategory } = useApiStore();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: 'Folder',
    color: '#1890ff',
  });

  // 当分类数据变化时更新表单
  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name,
        description: category.description,
        icon: category.icon,
        color: category.color,
      });
    }
  }, [category]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!category) return;
    
    updateCategory(category.id, formData);
    onClose();
  };

  const handleClose = () => {
    // 重置表单
    if (category) {
      setFormData({
        name: category.name,
        description: category.description,
        icon: category.icon,
        color: category.color,
      });
    }
    onClose();
  };

  if (!isOpen || !category) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <h2 className="text-xl font-semibold text-gray-900">编辑分类</h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form - 可滚动区域 */}
        <div className="flex-1 overflow-y-auto">
          <form id="edit-category-form" onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* 分类名称 */}
            <div>
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                <Type className="w-4 h-4" />
                <span>分类名称</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="输入分类名称"
                required
              />
            </div>

            {/* 分类描述 */}
            <div>
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                <FileText className="w-4 h-4" />
                <span>分类描述</span>
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={3}
                placeholder="输入分类描述"
                required
              />
            </div>

            {/* 图标选择 */}
            <div>
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                <Tag className="w-4 h-4" />
                <span>图标</span>
              </label>
              <div className="grid grid-cols-5 gap-2">
                {iconOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <button
                      key={option.value}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, icon: option.value }))}
                      className={`p-3 rounded-lg border-2 transition-colors flex flex-col items-center space-y-1 ${
                        formData.icon === option.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <IconComponent className="w-5 h-5" />
                      <span className="text-xs text-gray-600">{option.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* 颜色选择 */}
            <div>
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
                <Palette className="w-4 h-4" />
                <span>颜色</span>
              </label>
              <div className="grid grid-cols-6 gap-2">
                {colorOptions.map((color) => (
                  <button
                    key={color}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, color }))}
                    className={`w-10 h-10 rounded-lg border-2 transition-all ${
                      formData.color === color
                        ? 'border-gray-400 scale-110'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
              
              {/* 自定义颜色输入 */}
              <div className="mt-3">
                <input
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                  className="w-full h-10 rounded-lg border border-gray-300 cursor-pointer"
                />
              </div>
            </div>

            {/* 预览 */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">预览</label>
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div 
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: `${formData.color}20` }}
                >
                  {(() => {
                    const IconComponent = iconOptions.find(opt => opt.value === formData.icon)?.icon || Folder;
                    return <IconComponent className="w-5 h-5" style={{ color: formData.color }} />;
                  })()}
                </div>
                <div>
                  <div className="font-medium text-gray-900">{formData.name || '分类名称'}</div>
                  <div className="text-sm text-gray-500">{formData.description || '分类描述'}</div>
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* 按钮区域 - 固定在底部 */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 flex-shrink-0">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            form="edit-category-form"
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>保存</span>
          </button>
        </div>
      </div>
    </div>
  );
};
