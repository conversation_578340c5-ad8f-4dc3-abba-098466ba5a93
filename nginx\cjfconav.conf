server {
    listen 5173;
    server_name localhost;
    root /home/<USER>/dist;
    index index.html;

    # API 代理配置 - 代理到后端服务
    location /api/ {
        proxy_pass http://localhost:3001/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 错误处理
        proxy_intercept_errors on;
        error_page 502 503 504 = @api_fallback;
    }

    # 健康检查代理
    location /health {
        proxy_pass http://localhost:3001/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 健康检查不需要缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # API 错误回退处理
    location @api_fallback {
        add_header Content-Type application/json;
        return 503 '{"success": false, "error": "Backend service unavailable", "message": "后端服务暂时不可用，请稍后重试"}';
    }

    # 前端路由 - 必须放在 API 配置之后
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存优化
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 配置文件缓存
    location /config/ {
        expires 1h;
        add_header Cache-Control "public";
    }

    # Logo 和图片资源
    location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public";
        access_log off;
    }

    # CSS 和 JS 文件缓存
    location ~* \.(css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 启用 Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 日志配置
    access_log /var/log/nginx/cjfconav.access.log;
    error_log /var/log/nginx/cjfconav.error.log;

    # 添加到 Nginx 配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 错误页面 - 所有错误都重定向到 index.html (SPA 应用)
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;
}