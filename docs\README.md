# 长江期货导航系统 文档索引

## 📚 文档结构

本项目的文档按照功能和类型进行了分类整理，方便查找和维护。

## 🏗️ 架构与设计

### 系统架构
- [**ARCHITECTURE.md**](./ARCHITECTURE.md) - 系统整体架构设计
- [**VERSION-DISPLAY.md**](./VERSION-DISPLAY.md) - 版本信息显示功能

### 部署相关
- [**DEPLOYMENT-GUIDE.md**](./DEPLOYMENT-GUIDE.md) - 部署指南
- [**SERVER-DEPLOYMENT.md**](./SERVER-DEPLOYMENT.md) - 服务器部署详细说明
- [**DEV-ENVIRONMENT-SETUP.md**](./DEV-ENVIRONMENT-SETUP.md) - 开发环境搭建

## 🎨 前端相关

### 布局与样式
- [**HEADER-LAYOUT-OPTIMIZATION.md**](./HEADER-LAYOUT-OPTIMIZATION.md) - 头部布局优化
- [**LAYOUT-STYLE-RESTORED.md**](./LAYOUT-STYLE-RESTORED.md) - 布局样式恢复
- [**LAYOUT-HEIGHT-OPTIMIZED.md**](./LAYOUT-HEIGHT-OPTIMIZED.md) - 布局高度优化
- [**SCROLLBAR-STYLE-FIXED.md**](./SCROLLBAR-STYLE-FIXED.md) - 滚动条样式修复
- [**RIGHT-GRAY-BAR-FIXED.md**](./RIGHT-GRAY-BAR-FIXED.md) - 右侧灰色条修复
- [**GRAY-BAR-COMPREHENSIVE-FIX.md**](./GRAY-BAR-COMPREHENSIVE-FIX.md) - 灰色条综合修复

### 功能修复
- [**FRONTEND-ERROR-FIXED.md**](./FRONTEND-ERROR-FIXED.md) - 前端错误修复
- [**IPMANAGER-ERROR-FIXED.md**](./IPMANAGER-ERROR-FIXED.md) - IP管理器错误修复
- [**DUPLICATE-CATEGORIES-FIXED.md**](./DUPLICATE-CATEGORIES-FIXED.md) - 重复分类显示修复
- [**FAVORITES-FIX.md**](./FAVORITES-FIX.md) - 收藏功能修复

### 功能增强
- [**CATEGORY-MANAGEMENT.md**](./CATEGORY-MANAGEMENT.md) - 分类管理功能
- [**FEATURE-IMPROVEMENTS.md**](./FEATURE-IMPROVEMENTS.md) - 功能改进说明

## 🔧 后端相关

### 数据管理
- [**DATA-PERSISTENCE-FIX.md**](./DATA-PERSISTENCE-FIX.md) - 数据持久化修复
- [**SAMPLE-DATA-CREATED.md**](./SAMPLE-DATA-CREATED.md) - 示例数据创建

## 🧪 测试相关

### 前后端集成
- [**FRONTEND-BACKEND-TEST.md**](./FRONTEND-BACKEND-TEST.md) - 前后端连接测试

## 📁 目录结构

```
长江期货导航系统/
├── docs/                          # 主要文档目录
│   ├── README.md                   # 文档索引 (本文件)
│   ├── ARCHITECTURE.md             # 系统架构
│   ├── DEPLOYMENT-GUIDE.md         # 部署指南
│   ├── DEV-ENVIRONMENT-SETUP.md    # 开发环境
│   └── ...                        # 其他功能文档
├── server/
│   └── docs/                      # 后端专用文档
│       ├── README-DEV.md          # 后端开发说明
│       └── ISSUE-FIXED.md         # 后端问题修复
└── README.md                      # 项目主说明文档
```

## 🔍 快速查找

### 按问题类型查找

**布局问题**:
- 头部布局 → [HEADER-LAYOUT-OPTIMIZATION.md](./HEADER-LAYOUT-OPTIMIZATION.md)
- 高度问题 → [LAYOUT-HEIGHT-OPTIMIZED.md](./LAYOUT-HEIGHT-OPTIMIZED.md)
- 灰色条问题 → [RIGHT-GRAY-BAR-FIXED.md](./RIGHT-GRAY-BAR-FIXED.md)

**功能问题**:
- 收藏功能 → [FAVORITES-FIX.md](./FAVORITES-FIX.md)
- 分类管理 → [CATEGORY-MANAGEMENT.md](./CATEGORY-MANAGEMENT.md)
- 重复显示 → [DUPLICATE-CATEGORIES-FIXED.md](./DUPLICATE-CATEGORIES-FIXED.md)

**错误修复**:
- 前端错误 → [FRONTEND-ERROR-FIXED.md](./FRONTEND-ERROR-FIXED.md)
- IP管理错误 → [IPMANAGER-ERROR-FIXED.md](./IPMANAGER-ERROR-FIXED.md)

**部署相关**:
- 部署指南 → [DEPLOYMENT-GUIDE.md](./DEPLOYMENT-GUIDE.md)
- 服务器部署 → [SERVER-DEPLOYMENT.md](./SERVER-DEPLOYMENT.md)
- 开发环境 → [DEV-ENVIRONMENT-SETUP.md](./DEV-ENVIRONMENT-SETUP.md)

### 按开发阶段查找

**项目初始化**:
1. [DEV-ENVIRONMENT-SETUP.md](./DEV-ENVIRONMENT-SETUP.md) - 环境搭建
2. [ARCHITECTURE.md](./ARCHITECTURE.md) - 了解架构
3. [SAMPLE-DATA-CREATED.md](./SAMPLE-DATA-CREATED.md) - 示例数据

**开发调试**:
1. [FRONTEND-BACKEND-TEST.md](./FRONTEND-BACKEND-TEST.md) - 测试连接
2. [FRONTEND-ERROR-FIXED.md](./FRONTEND-ERROR-FIXED.md) - 错误排查
3. [DATA-PERSISTENCE-FIX.md](./DATA-PERSISTENCE-FIX.md) - 数据问题

**部署上线**:
1. [DEPLOYMENT-GUIDE.md](./DEPLOYMENT-GUIDE.md) - 部署准备
2. [SERVER-DEPLOYMENT.md](./SERVER-DEPLOYMENT.md) - 服务器配置

## 📝 文档维护

### 文档更新原则
- 每次重要功能修改都应更新相关文档
- 新增功能需要创建对应的说明文档
- 问题修复需要记录修复过程和方案

### 文档命名规范
- 功能说明: `FEATURE-NAME.md`
- 问题修复: `ISSUE-NAME-FIXED.md`
- 部署相关: `DEPLOYMENT-*.md`
- 开发相关: `DEV-*.md`

### 文档分类
- **架构设计**: 系统整体设计和技术选型
- **功能说明**: 具体功能的实现和使用
- **问题修复**: 遇到的问题和解决方案
- **部署运维**: 部署、配置和维护相关

## 🎯 使用建议

1. **新开发者**: 先阅读 [ARCHITECTURE.md](./ARCHITECTURE.md) 和 [DEV-ENVIRONMENT-SETUP.md](./DEV-ENVIRONMENT-SETUP.md)
2. **遇到问题**: 在对应的修复文档中查找解决方案
3. **部署项目**: 参考 [DEPLOYMENT-GUIDE.md](./DEPLOYMENT-GUIDE.md)
4. **功能开发**: 查看相关的功能说明文档

## 📞 支持

如果在文档中没有找到需要的信息，可以：
1. 检查是否有相关的修复文档
2. 查看项目的 README.md
3. 查看代码中的注释说明
