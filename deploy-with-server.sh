#!/bin/bash

# CJFCO Nav 完整部署脚本（前端 + 后端）

set -e  # 遇到错误立即退出

echo "🚀 开始部署 CJFCO Nav 完整系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
FRONTEND_PORT=${FRONTEND_PORT:-5173}
BACKEND_PORT=${BACKEND_PORT:-3001}
DEPLOY_DIR=${DEPLOY_DIR:-/home/<USER>
NGINX_CONFIG_DIR=${NGINX_CONFIG_DIR:-/etc/nginx/sites-available}
NGINX_ENABLED_DIR=${NGINX_ENABLED_DIR:-/etc/nginx/sites-enabled}

echo -e "${BLUE}📋 部署配置:${NC}"
echo -e "   前端端口: ${FRONTEND_PORT}"
echo -e "   后端端口: ${BACKEND_PORT}"
echo -e "   部署目录: ${DEPLOY_DIR}"
echo ""

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}🔍 检查系统依赖...${NC}"
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js 16+${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js 版本: ${NODE_VERSION}${NC}"
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm 版本: ${NPM_VERSION}${NC}"
    
    # 检查 nginx
    if ! command -v nginx &> /dev/null; then
        echo -e "${YELLOW}⚠️  nginx 未安装，将跳过 nginx 配置${NC}"
        SKIP_NGINX=true
    else
        NGINX_VERSION=$(nginx -v 2>&1 | cut -d' ' -f3)
        echo -e "${GREEN}✅ nginx 版本: ${NGINX_VERSION}${NC}"
        SKIP_NGINX=false
    fi
    
    echo ""
}

# 构建前端
build_frontend() {
    echo -e "${BLUE}🏗️  构建前端应用...${NC}"
    
    # 安装前端依赖
    echo "📦 安装前端依赖..."
    npm install
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 前端依赖安装失败${NC}"
        exit 1
    fi
    
    # 构建前端
    echo "🔨 构建前端应用..."
    npm run build
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 前端构建失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 前端构建完成${NC}"
    echo ""
}

# 部署后端
deploy_backend() {
    echo -e "${BLUE}🖥️  部署后端服务...${NC}"
    
    # 创建后端部署目录
    BACKEND_DIR="${DEPLOY_DIR}/server"
    sudo mkdir -p "$BACKEND_DIR"
    
    # 复制后端文件
    echo "📁 复制后端文件..."
    sudo cp -r server/* "$BACKEND_DIR/"
    
    # 进入后端目录安装依赖
    cd "$BACKEND_DIR"
    echo "📦 安装后端依赖..."
    sudo npm install --production
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 后端依赖安装失败${NC}"
        exit 1
    fi
    
    # 创建数据目录
    sudo mkdir -p "$BACKEND_DIR/data"
    sudo mkdir -p "$BACKEND_DIR/logs"
    
    # 设置权限
    sudo chown -R www-data:www-data "$BACKEND_DIR"
    sudo chmod +x "$BACKEND_DIR/start.sh"
    
    echo -e "${GREEN}✅ 后端部署完成${NC}"
    echo ""
    
    # 返回原目录
    cd - > /dev/null
}

# 部署前端
deploy_frontend() {
    echo -e "${BLUE}🌐 部署前端应用...${NC}"
    
    # 创建前端部署目录
    FRONTEND_DIR="${DEPLOY_DIR}/dist"
    sudo mkdir -p "$FRONTEND_DIR"
    
    # 复制前端构建文件
    echo "📁 复制前端文件..."
    sudo cp -r dist/* "$FRONTEND_DIR/"
    
    # 设置权限
    sudo chown -R www-data:www-data "$FRONTEND_DIR"
    sudo chmod -R 755 "$FRONTEND_DIR"
    
    echo -e "${GREEN}✅ 前端部署完成${NC}"
    echo ""
}

# 配置 nginx
configure_nginx() {
    if [ "$SKIP_NGINX" = true ]; then
        echo -e "${YELLOW}⏭️  跳过 nginx 配置${NC}"
        return
    fi
    
    echo -e "${BLUE}⚙️  配置 nginx...${NC}"
    
    # 创建 nginx 配置
    NGINX_CONFIG="$NGINX_CONFIG_DIR/cjfconav.conf"
    
    sudo tee "$NGINX_CONFIG" > /dev/null <<EOF
# CJFCO Nav 完整配置 - 前端 + 后端
server {
    listen ${FRONTEND_PORT};
    server_name localhost;
    root ${DEPLOY_DIR}/dist;
    index index.html;
    
    # 前端路由支持
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # API 代理到后端
    location /api {
        proxy_pass http://localhost:${BACKEND_PORT};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 访问日志
    access_log /var/log/nginx/cjfconav.access.log;
    error_log /var/log/nginx/cjfconav.error.log;
}
EOF
    
    # 启用配置
    sudo ln -sf "$NGINX_CONFIG" "$NGINX_ENABLED_DIR/cjfconav.conf"
    
    # 测试配置
    echo "🧪 测试 nginx 配置..."
    sudo nginx -t
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ nginx 配置测试失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ nginx 配置完成${NC}"
    echo ""
}

# 启动服务
start_services() {
    echo -e "${BLUE}🚀 启动服务...${NC}"
    
    # 启动后端服务
    echo "🖥️  启动后端服务..."
    cd "${DEPLOY_DIR}/server"
    
    # 检查是否已有进程在运行
    if pgrep -f "node.*index.js" > /dev/null; then
        echo "⚠️  检测到后端服务已在运行，正在重启..."
        pkill -f "node.*index.js"
        sleep 2
    fi
    
    # 后台启动后端服务
    nohup node index.js > logs/server.log 2>&1 &
    BACKEND_PID=$!
    
    echo "🔍 等待后端服务启动..."
    sleep 3
    
    # 检查后端服务是否启动成功
    if curl -s "http://localhost:${BACKEND_PORT}/health" > /dev/null; then
        echo -e "${GREEN}✅ 后端服务启动成功 (PID: ${BACKEND_PID})${NC}"
    else
        echo -e "${RED}❌ 后端服务启动失败${NC}"
        exit 1
    fi
    
    # 重新加载 nginx
    if [ "$SKIP_NGINX" = false ]; then
        echo "🔄 重新加载 nginx..."
        sudo systemctl reload nginx
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ nginx 重新加载成功${NC}"
        else
            echo -e "${RED}❌ nginx 重新加载失败${NC}"
            exit 1
        fi
    fi
    
    echo ""
    cd - > /dev/null
}

# 验证部署
verify_deployment() {
    echo -e "${BLUE}🔍 验证部署...${NC}"
    
    # 检查后端健康状态
    echo "🖥️  检查后端服务..."
    if curl -s "http://localhost:${BACKEND_PORT}/health" | grep -q "ok"; then
        echo -e "${GREEN}✅ 后端服务运行正常${NC}"
    else
        echo -e "${RED}❌ 后端服务异常${NC}"
    fi
    
    # 检查前端访问
    if [ "$SKIP_NGINX" = false ]; then
        echo "🌐 检查前端服务..."
        if curl -s -I "http://localhost:${FRONTEND_PORT}" | grep -q "200 OK"; then
            echo -e "${GREEN}✅ 前端服务运行正常${NC}"
        else
            echo -e "${RED}❌ 前端服务异常${NC}"
        fi
    fi
    
    echo ""
}

# 显示部署结果
show_result() {
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo ""
    echo -e "${BLUE}📋 服务信息:${NC}"
    echo -e "   🌐 前端地址: http://localhost:${FRONTEND_PORT}"
    echo -e "   🖥️  后端地址: http://localhost:${BACKEND_PORT}"
    echo -e "   📁 部署目录: ${DEPLOY_DIR}"
    echo -e "   📊 后端日志: ${DEPLOY_DIR}/server/logs/server.log"
    echo ""
    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo -e "   查看后端日志: tail -f ${DEPLOY_DIR}/server/logs/server.log"
    echo -e "   重启后端服务: cd ${DEPLOY_DIR}/server && ./start.sh"
    echo -e "   重新加载 nginx: sudo systemctl reload nginx"
    echo ""
    echo -e "${YELLOW}💡 提示: 首次访问可能需要等待几秒钟让服务完全启动${NC}"
}

# 主执行流程
main() {
    check_dependencies
    build_frontend
    deploy_backend
    deploy_frontend
    configure_nginx
    start_services
    verify_deployment
    show_result
}

# 执行主流程
main
