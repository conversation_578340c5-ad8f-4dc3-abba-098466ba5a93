// 访问统计工具 - 只有成功打开页面才计数

interface AccessRecord {
  itemId: string;
  url: string;
  timestamp: number;
  success: boolean;
  userAgent: string;
}

class AccessTracker {
  private static instance: AccessTracker;
  private accessRecordsKey = 'fintech-nav-access';
  private pendingChecks = new Map<string, NodeJS.Timeout>();

  private constructor() {}

  public static getInstance(): AccessTracker {
    if (!AccessTracker.instance) {
      AccessTracker.instance = new AccessTracker();
    }
    return AccessTracker.instance;
  }

  // 获取访问记录
  private getAccessRecords(): AccessRecord[] {
    try {
      const records = localStorage.getItem(this.accessRecordsKey);
      return records ? JSON.parse(records) : [];
    } catch (error) {
      console.error('Error parsing access records:', error);
      return [];
    }
  }

  // 保存访问记录
  private saveAccessRecords(records: AccessRecord[]): void {
    try {
      localStorage.setItem(this.accessRecordsKey, JSON.stringify(records));
    } catch (error) {
      console.error('Error saving access records:', error);
    }
  }

  // 尝试打开链接并跟踪访问
  public trackAccess(itemId: string, url: string, onSuccess?: () => void): boolean {
    try {
      // 尝试打开新窗口
      const newWindow = window.open(url, '_blank', 'noopener,noreferrer');
      
      if (!newWindow) {
        // 弹窗被阻止
        this.recordAccess(itemId, url, false);
        return false;
      }

      // 记录尝试访问
      const attemptId = `${itemId}-${Date.now()}`;
      
      // 设置检查定时器
      const checkTimer = setTimeout(() => {
        this.checkWindowStatus(attemptId, itemId, url, newWindow, onSuccess);
      }, 500); // 500ms 后检查

      this.pendingChecks.set(attemptId, checkTimer);
      
      return true;
    } catch (error) {
      console.error('Error opening window:', error);
      this.recordAccess(itemId, url, false);
      return false;
    }
  }

  // 检查窗口状态
  private checkWindowStatus(
    attemptId: string,
    itemId: string,
    url: string,
    windowRef: Window,
    onSuccess?: () => void
  ): void {
    try {
      // 清除定时器
      const timer = this.pendingChecks.get(attemptId);
      if (timer) {
        clearTimeout(timer);
        this.pendingChecks.delete(attemptId);
      }

      // 检查窗口是否仍然打开
      if (windowRef.closed) {
        // 窗口已关闭，可能是立即关闭的，认为失败
        this.recordAccess(itemId, url, false);
        return;
      }

      // 尝试访问窗口的 location（同源策略限制）
      let success = true;
      try {
        // 如果能访问 location，说明页面加载成功
        const location = windowRef.location.href;
        if (location === 'about:blank') {
          // 仍在加载中，延迟再次检查
          setTimeout(() => {
            this.checkWindowStatus(attemptId, itemId, url, windowRef, onSuccess);
          }, 1000);
          return;
        }
      } catch (e) {
        // 跨域访问被阻止，这实际上表明页面已经开始加载
        // 这是正常情况，认为成功
        success = true;
      }

      if (success) {
        this.recordAccess(itemId, url, true);
        onSuccess?.();
      } else {
        this.recordAccess(itemId, url, false);
      }
    } catch (error) {
      console.error('Error checking window status:', error);
      this.recordAccess(itemId, url, false);
    }
  }

  // 记录访问
  private recordAccess(itemId: string, url: string, success: boolean): void {
    const records = this.getAccessRecords();
    
    const newRecord: AccessRecord = {
      itemId,
      url,
      timestamp: Date.now(),
      success,
      userAgent: navigator.userAgent,
    };

    records.push(newRecord);
    
    // 只保留最近1000条记录
    if (records.length > 1000) {
      records.splice(0, records.length - 1000);
    }

    this.saveAccessRecords(records);
  }

  // 获取成功访问次数
  public getSuccessfulAccessCount(itemId: string): number {
    const records = this.getAccessRecords();
    return records.filter(record => record.itemId === itemId && record.success).length;
  }

  // 获取总访问次数（包括失败的）
  public getTotalAccessCount(itemId: string): number {
    const records = this.getAccessRecords();
    return records.filter(record => record.itemId === itemId).length;
  }

  // 获取访问统计
  public getAccessStats(itemId: string): {
    total: number;
    successful: number;
    failed: number;
    successRate: number;
    lastAccess?: Date;
  } {
    const records = this.getAccessRecords();
    const itemRecords = records.filter(record => record.itemId === itemId);
    
    const total = itemRecords.length;
    const successful = itemRecords.filter(record => record.success).length;
    const failed = total - successful;
    const successRate = total > 0 ? (successful / total) * 100 : 0;
    
    const lastAccessRecord = itemRecords
      .sort((a, b) => b.timestamp - a.timestamp)[0];
    const lastAccess = lastAccessRecord ? new Date(lastAccessRecord.timestamp) : undefined;

    return {
      total,
      successful,
      failed,
      successRate,
      lastAccess,
    };
  }

  // 获取全局统计
  public getGlobalStats(): {
    totalAttempts: number;
    successfulAttempts: number;
    failedAttempts: number;
    overallSuccessRate: number;
    uniqueItems: number;
  } {
    const records = this.getAccessRecords();
    
    const totalAttempts = records.length;
    const successfulAttempts = records.filter(record => record.success).length;
    const failedAttempts = totalAttempts - successfulAttempts;
    const overallSuccessRate = totalAttempts > 0 ? (successfulAttempts / totalAttempts) * 100 : 0;
    
    const uniqueItems = new Set(records.map(record => record.itemId)).size;

    return {
      totalAttempts,
      successfulAttempts,
      failedAttempts,
      overallSuccessRate,
      uniqueItems,
    };
  }

  // 清理旧记录
  public cleanupOldRecords(daysOld: number = 30): void {
    const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    const records = this.getAccessRecords();
    
    const filteredRecords = records.filter(record => record.timestamp > cutoffTime);
    this.saveAccessRecords(filteredRecords);
  }

  // 导出访问数据
  public exportAccessData(): string {
    const records = this.getAccessRecords();
    return JSON.stringify(records, null, 2);
  }

  // 清除所有访问记录
  public clearAllRecords(): void {
    localStorage.removeItem(this.accessRecordsKey);
    
    // 清除所有待处理的检查
    this.pendingChecks.forEach(timer => clearTimeout(timer));
    this.pendingChecks.clear();
  }
}

// 导出单例实例
export const accessTracker = AccessTracker.getInstance();

// 在开发环境中添加调试工具
if (import.meta.env.DEV) {
  (window as any).accessTracker = accessTracker;
  console.log('📊 访问跟踪器已加载');
}
