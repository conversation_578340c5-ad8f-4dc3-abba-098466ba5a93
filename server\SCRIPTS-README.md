# 服务器管理脚本说明

## 📁 脚本文件

- **start.sh** - 服务器启动脚本 (支持前台/后台运行)
- **stop.sh** - 服务器停止脚本
- **status.sh** - 服务器状态检查脚本

## 🚀 使用方法

### 1. 设置可执行权限

```bash
chmod +x start.sh stop.sh status.sh
```

### 2. 启动服务器

**前台运行** (开发调试):
```bash
./start.sh
```

**后台运行** (生产环境):
```bash
./start.sh -d
```

**强制重启**:
```bash
./start.sh -f -d
```

### 3. 停止服务器

**优雅停止**:
```bash
./stop.sh
```

**强制停止**:
```bash
./stop.sh -f
```

### 4. 检查状态

**基本状态**:
```bash
./status.sh
```

**包含日志**:
```bash
./status.sh -l
```

**指定日志行数**:
```bash
./status.sh -l -n 50
```

## 🔧 功能特性

### start.sh 功能

**环境检查**:
- ✅ Node.js 版本检查
- ✅ 依赖包自动安装
- ✅ 数据目录自动创建
- ✅ 日志目录自动创建

**进程管理**:
- ✅ PID 文件管理
- ✅ 重复启动检测
- ✅ 强制重启支持
- ✅ 优雅停止机制

**日志记录**:
- ✅ 应用日志: `logs/app.log`
- ✅ 访问日志: `logs/access.log`
- ✅ 错误日志: `logs/error.log`
- ✅ PID 文件: `logs/server.pid`

**运行模式**:
- 🔧 **前台模式**: 直接运行，Ctrl+C 停止
- 🔧 **后台模式**: 守护进程运行，日志输出到文件

### stop.sh 功能

**停止方式**:
- 🤝 **优雅停止**: 发送 SIGTERM 信号，等待进程自然结束
- ⚡ **强制停止**: 发送 SIGKILL 信号，立即终止进程

**安全检查**:
- ✅ PID 文件验证
- ✅ 进程存在性检查
- ✅ 自动清理无效 PID 文件
- ✅ 进程搜索和提示

### status.sh 功能

**状态检查**:
- 📊 进程运行状态
- 🌐 端口监听状态
- 🏥 服务健康检查
- 📁 文件系统状态
- 🔧 环境依赖检查

**信息显示**:
- 📋 进程详细信息 (PID, CPU, 内存, 运行时间)
- 📊 服务健康信息 (JSON 格式)
- 📁 数据文件统计
- 📋 日志文件统计

## 📂 目录结构

```
server/
├── start.sh              # 启动脚本
├── stop.sh               # 停止脚本
├── status.sh             # 状态检查脚本
├── logs/                 # 日志目录 (自动创建)
│   ├── app.log           # 应用日志
│   ├── access.log        # 访问日志
│   ├── error.log         # 错误日志
│   └── server.pid        # PID 文件
├── data/                 # 数据目录 (自动创建)
│   ├── navItems.json     # 导航项目数据
│   ├── categories.json   # 分类数据
│   ├── stats.json        # 统计数据
│   └── config.json       # 配置数据
└── node_modules/         # 依赖包 (自动安装)
```

## 🎯 使用场景

### 开发环境

```bash
# 前台运行，便于调试
./start.sh

# 查看实时状态
./status.sh -l
```

### 生产环境

```bash
# 后台启动
./start.sh -d

# 定期检查状态
./status.sh

# 查看日志
tail -f logs/app.log

# 优雅停止
./stop.sh
```

### 维护操作

```bash
# 重启服务
./stop.sh && ./start.sh -d

# 强制重启
./start.sh -f -d

# 检查服务健康
./status.sh

# 查看最近错误
./status.sh -l -n 100 | grep -i error
```

## 🔍 故障排除

### 1. 启动失败

**检查步骤**:
```bash
# 查看详细状态
./status.sh -l

# 检查端口占用
netstat -tlnp | grep 3001

# 检查 Node.js 版本
node --version

# 检查依赖
npm list
```

### 2. 进程无响应

**解决方案**:
```bash
# 强制停止
./stop.sh -f

# 清理 PID 文件
rm -f logs/server.pid

# 重新启动
./start.sh -d
```

### 3. 日志文件过大

**清理方案**:
```bash
# 备份日志
cp logs/app.log logs/app.log.backup

# 清空日志
> logs/app.log

# 或者删除旧日志
rm logs/*.log
```

## 📊 监控建议

### 1. 定期健康检查

```bash
# 创建监控脚本
#!/bin/bash
cd /path/to/server
./status.sh > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "服务器异常，尝试重启..."
    ./start.sh -f -d
fi
```

### 2. 日志轮转

```bash
# 添加到 crontab
0 0 * * * cd /path/to/server && mv logs/app.log logs/app.log.$(date +\%Y\%m\%d) && touch logs/app.log
```

### 3. 系统服务集成

```bash
# 创建 systemd 服务文件
[Unit]
Description=次席管理平台
After=network.target

[Service]
Type=forking
User=www-data
WorkingDirectory=/path/to/server
ExecStart=/path/to/server/start.sh -d
ExecStop=/path/to/server/stop.sh
Restart=always

[Install]
WantedBy=multi-user.target
```

## 🎉 最佳实践

1. **开发时使用前台模式**，便于查看实时日志
2. **生产环境使用后台模式**，配合日志文件
3. **定期检查服务状态**，确保服务正常运行
4. **配置日志轮转**，避免日志文件过大
5. **使用强制重启**，解决进程卡死问题
6. **监控磁盘空间**，确保日志和数据目录有足够空间

现在您可以使用这些脚本来方便地管理服务器了！
