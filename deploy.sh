#!/bin/bash

# 次席管理平台 部署脚本
# 用于将应用部署到 /home/<USER>

set -e  # 遇到错误立即退出

echo "🚀 开始部署 次席管理平台..."

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用 sudo 运行此脚本"
    exit 1
fi

# 1. 构建项目
echo "📦 构建项目..."
npm run build

# 2. 创建部署目录
echo "📁 创建部署目录..."
mkdir -p /home/<USER>
mkdir -p /var/log/nginx

# 3. 复制构建文件
echo "📋 复制构建文件到部署目录..."
cp -r dist/* /home/<USER>/

# 4. 设置文件权限
echo "🔐 设置文件权限..."
chown -R www-data:www-data /home/<USER>
chmod -R 755 /home/<USER>

# 5. 复制 nginx 配置
echo "⚙️ 配置 Nginx..."
cp nginx/cjfconav.conf /etc/nginx/sites-available/
ln -sf /etc/nginx/sites-available/cjfconav.conf /etc/nginx/sites-enabled/

# 6. 测试 nginx 配置
echo "🧪 测试 Nginx 配置..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx 配置测试通过"
    
    # 7. 重新加载 nginx
    echo "🔄 重新加载 Nginx..."
    systemctl reload nginx
    
    # 8. 检查服务状态
    echo "📊 检查服务状态..."
    systemctl status nginx --no-pager -l
    
    echo ""
    echo "🎉 部署完成！"
    echo "📍 应用已部署到: /home/<USER>"
    echo "🌐 访问地址: http://localhost:5173"
    echo "📝 访问日志: /var/log/nginx/cjfconav.access.log"
    echo "❌ 错误日志: /var/log/nginx/cjfconav.error.log"
    echo ""
    echo "🔧 常用命令："
    echo "  查看日志: sudo tail -f /var/log/nginx/cjfconav.access.log"
    echo "  重启服务: sudo systemctl reload nginx"
    echo "  检查状态: sudo systemctl status nginx"
    
else
    echo "❌ Nginx 配置测试失败，请检查配置文件"
    exit 1
fi
