import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// 从环境变量读取配置，如果没有则使用默认值
const port = process.env.VITE_PORT ? parseInt(process.env.VITE_PORT) : 5173;
const host = process.env.VITE_HOST || 'localhost';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port,
    host,
    open: true,
    cors: true,
  },
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  // 确保配置文件可以被访问
  publicDir: 'public',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    target: 'es2020',
    minify: 'esbuild',
    sourcemap: false,
  },
  esbuild: {
    target: 'es2020',
  },
});
