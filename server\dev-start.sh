#!/bin/bash

# CJFCO Nav 开发环境后端启动脚本

echo "🚀 启动 CJFCO Nav 开发环境后端服务..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 进入脚本所在目录
cd "$(dirname "$0")"

# 检查Node.js版本
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到 Node.js，请先安装 Node.js 16+ 版本${NC}"
    exit 1
fi

NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js 版本: ${NODE_VERSION}${NC}"

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 安装开发依赖...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
fi

# 创建开发环境数据目录
mkdir -p data
mkdir -p logs

echo -e "${BLUE}📁 数据目录: $(pwd)/data${NC}"
echo -e "${BLUE}📊 日志目录: $(pwd)/logs${NC}"

# 设置开发环境变量
export NODE_ENV=development
export PORT=${PORT:-3001}
export DEBUG=true

echo -e "${BLUE}🔧 开发环境配置:${NC}"
echo -e "   - 环境: ${NODE_ENV}"
echo -e "   - 端口: ${PORT}"
echo -e "   - 调试模式: ${DEBUG}"
echo -e "   - 热重载: 启用"

# 检查端口是否被占用
if lsof -Pi :${PORT} -sTCP:LISTEN -t >/dev/null ; then
    echo -e "${YELLOW}⚠️  端口 ${PORT} 已被占用，正在尝试终止占用进程...${NC}"
    lsof -ti:${PORT} | xargs kill -9 2>/dev/null || true
    sleep 2
fi

echo ""
echo -e "${GREEN}🌟 启动开发服务器...${NC}"
echo -e "${BLUE}📍 服务地址: http://localhost:${PORT}${NC}"
echo -e "${BLUE}🔍 健康检查: http://localhost:${PORT}/health${NC}"
echo -e "${BLUE}📚 API文档: http://localhost:${PORT}/api${NC}"
echo ""
echo -e "${YELLOW}💡 开发提示:${NC}"
echo -e "   - 文件修改会自动重启服务器"
echo -e "   - 使用 Ctrl+C 停止服务器"
echo -e "   - 日志文件: logs/dev.log"
echo ""

# 启动开发服务器
npm run dev
