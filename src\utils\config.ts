// 配置类型定义
export interface AppConfig {
  server: {
    port: number;
    host: string;
    open: boolean;
    cors: boolean;
  };
  app: {
    title: string;
    description: string;
    version: string;
    sessionTimeout: number;
    autoSave: boolean;
    theme: {
      primaryColor: string;
      secondaryColor: string;
      darkMode: boolean;
    };
  };
  features: {
    enableSearch: boolean;
    enableFavorites: boolean;
    enableStats: boolean;
    enableAdminMode: boolean;
    enableNotifications: boolean;
  };
  security: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
    enableCSRF: boolean;
  };
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
  };
  storage: {
    type: 'localStorage' | 'sessionStorage';
    prefix: string;
    encryption: boolean;
  };
}

// 默认配置
const defaultConfig: AppConfig = {
  server: {
    port: 5173,
    host: 'localhost',
    open: true,
    cors: true,
  },
  app: {
    title: 'CJFCO Nav - 长江期货导航系统',
    description: '现代化的企业级导航管理系统，为金融科技团队提供统一的系统入口和资源管理',
    version: '1.0.0',
    sessionTimeout: 30,
    autoSave: true,
    theme: {
      primaryColor: '#0EA5E9',
      secondaryColor: '#38BDF8',
      darkMode: false,
    },
  },
  features: {
    enableSearch: true,
    enableFavorites: true,
    enableStats: true,
    enableAdminMode: true,
    enableNotifications: true,
  },
  security: {
    sessionTimeout: 1800, // 30分钟
    maxLoginAttempts: 5,
    lockoutDuration: 300, // 5分钟
    enableCSRF: true,
  },
  api: {
    baseUrl: '/api',
    timeout: 10000,
    retryAttempts: 3,
  },
  storage: {
    type: 'localStorage',
    prefix: 'fintech-nav-',
    encryption: false,
  },
};

// 配置管理类
class ConfigManager {
  private config: AppConfig = defaultConfig;
  private loaded = false;

  // 加载配置
  async loadConfig(): Promise<AppConfig> {
    if (this.loaded) {
      return this.config;
    }

    try {
      // 尝试从环境变量读取配置
      const envConfig = this.loadFromEnv();
      
      // 尝试从配置文件读取配置
      const fileConfig = await this.loadFromFile();
      
      // 合并配置（优先级：环境变量 > 配置文件 > 默认配置）
      this.config = this.mergeConfigs(defaultConfig, fileConfig, envConfig);
      this.loaded = true;
      
      return this.config;
    } catch (error) {
      console.warn('Failed to load config, using default:', error);
      this.config = defaultConfig;
      this.loaded = true;
      return this.config;
    }
  }

  // 从环境变量加载配置
  private loadFromEnv(): Partial<AppConfig> {
    const envConfig: Partial<AppConfig> = {};

    // 服务器配置
    if (import.meta.env.VITE_PORT) {
      envConfig.server = {
        ...defaultConfig.server,
        port: parseInt(import.meta.env.VITE_PORT),
      };
    }

    // 应用配置
    if (import.meta.env.VITE_APP_TITLE || import.meta.env.VITE_SESSION_TIMEOUT) {
      envConfig.app = {
        ...defaultConfig.app,
        ...(import.meta.env.VITE_APP_TITLE && { title: import.meta.env.VITE_APP_TITLE }),
        ...(import.meta.env.VITE_SESSION_TIMEOUT && { 
          sessionTimeout: parseInt(import.meta.env.VITE_SESSION_TIMEOUT) 
        }),
      };
    }

    // API 配置
    if (import.meta.env.VITE_API_BASE_URL) {
      envConfig.api = {
        ...defaultConfig.api,
        baseUrl: import.meta.env.VITE_API_BASE_URL,
      };
    }

    return envConfig;
  }

  // 从配置文件加载配置
  private async loadFromFile(): Promise<Partial<AppConfig>> {
    try {
      const response = await fetch('/config/app.config.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.warn('Failed to load config file:', error);
      return {};
    }
  }

  // 合并配置对象
  private mergeConfigs(...configs: Partial<AppConfig>[]): AppConfig {
    return configs.reduce((merged, config) => {
      return this.deepMerge(merged, config);
    }, {} as AppConfig) as AppConfig;
  }

  // 深度合并对象
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  // 获取配置
  getConfig(): AppConfig {
    if (!this.loaded) {
      console.warn('Config not loaded yet, returning default config');
      return defaultConfig;
    }
    return this.config;
  }

  // 获取特定配置项
  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.getConfig()[key];
  }

  // 更新配置
  updateConfig(updates: Partial<AppConfig>): void {
    this.config = this.deepMerge(this.config, updates);
  }
}

// 导出配置管理器实例
export const configManager = new ConfigManager();

// 导出便捷方法
export const getConfig = () => configManager.getConfig();
export const loadConfig = () => configManager.loadConfig();
