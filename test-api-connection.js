// 测试前后端API连接的脚本

const API_BASE_URL = 'http://localhost:3001/api';

async function testApiConnection() {
  console.log('🔍 测试前后端API连接...\n');

  // 测试健康检查
  console.log('1. 测试健康检查...');
  try {
    const response = await fetch('http://localhost:3001/health');
    const data = await response.json();
    console.log('✅ 健康检查成功:', data);
  } catch (error) {
    console.log('❌ 健康检查失败:', error.message);
    return;
  }

  // 测试获取导航项目
  console.log('\n2. 测试获取导航项目...');
  try {
    const response = await fetch(`${API_BASE_URL}/nav-items`);
    const data = await response.json();
    console.log('✅ 获取导航项目成功:', data);
  } catch (error) {
    console.log('❌ 获取导航项目失败:', error.message);
  }

  // 测试获取分类
  console.log('\n3. 测试获取分类...');
  try {
    const response = await fetch(`${API_BASE_URL}/categories`);
    const data = await response.json();
    console.log('✅ 获取分类成功:', data);
  } catch (error) {
    console.log('❌ 获取分类失败:', error.message);
  }

  // 测试添加导航项目
  console.log('\n4. 测试添加导航项目...');
  try {
    const newItem = {
      name: '测试应用',
      url: 'https://example.com',
      description: '这是一个测试应用',
      category: 'all',
      tags: ['测试'],
      icon: 'Globe'
    };

    const response = await fetch(`${API_BASE_URL}/nav-items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newItem),
    });

    const data = await response.json();
    console.log('✅ 添加导航项目成功:', data);

    // 保存ID用于后续测试
    window.testItemId = data.data.id;
  } catch (error) {
    console.log('❌ 添加导航项目失败:', error.message);
  }

  // 测试获取统计数据
  console.log('\n5. 测试获取统计数据...');
  try {
    const response = await fetch(`${API_BASE_URL}/stats`);
    const data = await response.json();
    console.log('✅ 获取统计数据成功:', data);
  } catch (error) {
    console.log('❌ 获取统计数据失败:', error.message);
  }

  // 测试删除导航项目（如果之前添加成功）
  if (window.testItemId) {
    console.log('\n6. 测试删除导航项目...');
    try {
      const response = await fetch(`${API_BASE_URL}/nav-items/${window.testItemId}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      console.log('✅ 删除导航项目成功:', data);
    } catch (error) {
      console.log('❌ 删除导航项目失败:', error.message);
    }
  }

  console.log('\n🎉 API连接测试完成！');
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  window.testApiConnection = testApiConnection;
  console.log('💡 在浏览器控制台中运行: testApiConnection()');
} else {
  // Node.js 环境
  testApiConnection();
}
