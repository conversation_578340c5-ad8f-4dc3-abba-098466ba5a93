// IP地址管理和点赞限制工具

interface LikeRecord {
  itemId: string;
  timestamp: number;
}

class IPManager {
  private static instance: IPManager;
  private ipAddress: string | null = null;
  private likeRecordsKey = 'fintech-nav-likes';

  private constructor() {
    this.initializeIP();
  }

  public static getInstance(): IPManager {
    if (!IPManager.instance) {
      IPManager.instance = new IPManager();
    }
    return IPManager.instance;
  }

  // 初始化IP地址（模拟）
  private async initializeIP(): Promise<void> {
    try {
      // 在实际应用中，这里应该调用真实的IP获取API
      // 这里我们使用一个模拟的IP地址，基于浏览器指纹
      this.ipAddress = this.generateBrowserFingerprint();
    } catch (error) {
      console.warn('Failed to get IP address, using fallback:', error);
      this.ipAddress = this.generateBrowserFingerprint();
    }
  }

  // 生成浏览器指纹作为IP替代
  private generateBrowserFingerprint(): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Browser fingerprint', 2, 2);
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
      localStorage.length,
    ].join('|');

    // 生成简单的哈希
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    // 转换为类似IP的格式
    const ip = Math.abs(hash);
    return `192.168.${(ip >> 8) & 255}.${ip & 255}`;
  }

  // 获取当前IP地址
  public getIP(): string {
    return this.ipAddress || this.generateBrowserFingerprint();
  }

  // 获取点赞记录
  private getLikeRecords(): Record<string, LikeRecord[]> {
    try {
      const records = localStorage.getItem(this.likeRecordsKey);
      return records ? JSON.parse(records) : {};
    } catch (error) {
      console.error('Error parsing like records:', error);
      return {};
    }
  }

  // 保存点赞记录
  private saveLikeRecords(records: Record<string, LikeRecord[]>): void {
    try {
      localStorage.setItem(this.likeRecordsKey, JSON.stringify(records));
    } catch (error) {
      console.error('Error saving like records:', error);
    }
  }

  // 检查是否已经点赞
  public hasLiked(itemId: string): boolean {
    const ip = this.getIP();
    const records = this.getLikeRecords();
    const ipRecords = records[ip] || [];
    
    return ipRecords.some(record => record.itemId === itemId);
  }

  // 添加点赞记录
  public addLike(itemId: string): boolean {
    const ip = this.getIP();
    const records = this.getLikeRecords();
    
    if (!records[ip]) {
      records[ip] = [];
    }

    // 检查是否已经点赞
    if (this.hasLiked(itemId)) {
      return false; // 已经点赞过
    }

    // 添加点赞记录
    records[ip].push({
      itemId,
      timestamp: Date.now(),
    });

    this.saveLikeRecords(records);
    return true; // 点赞成功
  }

  // 移除点赞记录
  public removeLike(itemId: string): boolean {
    const ip = this.getIP();
    const records = this.getLikeRecords();
    
    if (!records[ip]) {
      return false; // 没有记录
    }

    const initialLength = records[ip].length;
    records[ip] = records[ip].filter(record => record.itemId !== itemId);

    if (records[ip].length === initialLength) {
      return false; // 没有找到要删除的记录
    }

    // 如果该IP没有任何点赞记录，删除该IP的记录
    if (records[ip].length === 0) {
      delete records[ip];
    }

    this.saveLikeRecords(records);
    return true; // 取消点赞成功
  }

  // 切换点赞状态
  public toggleLike(itemId: string): { liked: boolean; action: 'add' | 'remove' } {
    if (this.hasLiked(itemId)) {
      this.removeLike(itemId);
      return { liked: false, action: 'remove' };
    } else {
      this.addLike(itemId);
      return { liked: true, action: 'add' };
    }
  }

  // 获取IP的所有点赞记录
  public getIPLikes(): string[] {
    const ip = this.getIP();
    const records = this.getLikeRecords();
    const ipRecords = records[ip] || [];
    
    return ipRecords.map(record => record.itemId);
  }

  // 清理过期记录（可选，用于清理旧数据）
  public cleanupOldRecords(daysOld: number = 30): void {
    const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    const records = this.getLikeRecords();
    
    Object.keys(records).forEach(ip => {
      records[ip] = records[ip].filter(record => record.timestamp > cutoffTime);
      
      // 如果该IP没有任何记录，删除该IP
      if (records[ip].length === 0) {
        delete records[ip];
      }
    });

    this.saveLikeRecords(records);
  }

  // 获取统计信息
  public getStats(): { totalIPs: number; totalLikes: number; currentIPLikes: number } {
    const records = this.getLikeRecords();
    const currentIP = this.getIP();
    
    const totalIPs = Object.keys(records).length;
    const totalLikes = Object.values(records).reduce((sum, ipRecords) => sum + ipRecords.length, 0);
    const currentIPLikes = (records[currentIP] || []).length;

    return {
      totalIPs,
      totalLikes,
      currentIPLikes,
    };
  }
}

// 导出单例实例
export const ipManager = IPManager.getInstance();

// 在开发环境中添加调试工具
if (import.meta.env.DEV) {
  (window as any).ipManager = ipManager;
  console.log('🌐 IP管理器已加载，当前IP:', ipManager.getIP());
}
