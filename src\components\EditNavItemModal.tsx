import React, { useState, useEffect } from 'react';
import { NavItem } from '../types';
import { useAppStore } from '../store/useAppStore';
import { 
  X, 
  Save, 
  Link, 
  Type, 
  FileText, 
  Tag, 
  Palette,
  TrendingUp,
  Shield,
  BarChart3,
  Users,
  Calculator,
  Settings,
  Globe,
  Building
} from 'lucide-react';

const iconOptions = [
  { value: 'TrendingUp', label: '趋势', icon: TrendingUp },
  { value: 'Shield', label: '安全', icon: Shield },
  { value: 'BarChart3', label: '图表', icon: BarChart3 },
  { value: 'Users', label: '用户', icon: Users },
  { value: 'Calculator', label: '计算器', icon: Calculator },
  { value: 'Settings', label: '设置', icon: Settings },
  { value: 'Globe', label: '全球', icon: Globe },
  { value: 'Building', label: '建筑', icon: Building },
];

interface EditNavItemModalProps {
  isOpen: boolean;
  item: NavItem | null;
  onClose: () => void;
}

export const EditNavItemModal: React.FC<EditNavItemModalProps> = ({
  isOpen,
  item,
  onClose,
}) => {
  const { updateNavItem, categories } = useAppStore();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    url: '',
    icon: 'Settings',
    category: '',
    tags: [] as string[],
    isInternal: false,
  });
  
  const [tagInput, setTagInput] = useState('');
  const [customIcon, setCustomIcon] = useState('');
  const [showCustomIcon, setShowCustomIcon] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name,
        description: item.description,
        url: item.url,
        icon: item.icon,
        category: item.category,
        tags: [...item.tags],
        isInternal: item.isInternal,
      });
      
      // 检查是否使用自定义图标
      const isCustom = !iconOptions.some(opt => opt.value === item.icon);
      setShowCustomIcon(isCustom);
      if (isCustom) {
        setCustomIcon(item.icon);
      }
    }
  }, [item]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!item) return;

    const finalIcon = showCustomIcon ? customIcon : formData.icon;
    
    updateNavItem(item.id, {
      ...formData,
      icon: finalIcon,
      tags: formData.tags.filter(tag => tag.trim() !== ''),
    });
    
    onClose();
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  if (!isOpen || !item) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">编辑应用</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* 应用名称 */}
          <div>
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
              <Type className="w-4 h-4" />
              <span>应用名称</span>
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="输入应用名称"
              required
            />
          </div>

          {/* 应用描述 */}
          <div>
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
              <FileText className="w-4 h-4" />
              <span>应用描述</span>
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="输入应用描述"
              rows={3}
              required
            />
          </div>

          {/* URL */}
          <div>
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
              <Link className="w-4 h-4" />
              <span>访问地址</span>
            </label>
            <input
              type="url"
              value={formData.url}
              onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://example.com"
              required
            />
          </div>

          {/* 分类 */}
          <div>
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
              <Palette className="w-4 h-4" />
              <span>分类</span>
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">选择分类</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* 图标选择 */}
          <div>
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
              <Palette className="w-4 h-4" />
              <span>图标</span>
            </label>
            
            <div className="space-y-3">
              {/* 预设图标选择 */}
              <div>
                <label className="flex items-center space-x-2 mb-2">
                  <input
                    type="radio"
                    checked={!showCustomIcon}
                    onChange={() => setShowCustomIcon(false)}
                    className="text-blue-600"
                  />
                  <span className="text-sm text-gray-700">选择预设图标</span>
                </label>
                
                {!showCustomIcon && (
                  <div className="grid grid-cols-4 gap-2">
                    {iconOptions.map((option) => {
                      const IconComponent = option.icon;
                      return (
                        <button
                          key={option.value}
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, icon: option.value }))}
                          className={`p-3 border rounded-lg flex flex-col items-center space-y-1 transition-colors ${
                            formData.icon === option.value
                              ? 'border-blue-500 bg-blue-50 text-blue-600'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <IconComponent className="w-5 h-5" />
                          <span className="text-xs">{option.label}</span>
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* 自定义图标 */}
              <div>
                <label className="flex items-center space-x-2 mb-2">
                  <input
                    type="radio"
                    checked={showCustomIcon}
                    onChange={() => setShowCustomIcon(true)}
                    className="text-blue-600"
                  />
                  <span className="text-sm text-gray-700">自定义图标（Lucide 图标名称）</span>
                </label>
                
                {showCustomIcon && (
                  <input
                    type="text"
                    value={customIcon}
                    onChange={(e) => setCustomIcon(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如: Home, User, Mail"
                  />
                )}
              </div>
            </div>
          </div>

          {/* 标签 */}
          <div>
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 mb-2">
              <Tag className="w-4 h-4" />
              <span>标签</span>
            </label>
            
            <div className="space-y-2">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="输入标签后按回车添加"
                />
                <button
                  type="button"
                  onClick={handleAddTag}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  添加
                </button>
              </div>
              
              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 内部应用 */}
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.isInternal}
                onChange={(e) => setFormData(prev => ({ ...prev, isInternal: e.target.checked }))}
                className="text-blue-600"
              />
              <span className="text-sm text-gray-700">内部应用</span>
            </label>
          </div>

          {/* 按钮 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>保存</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
