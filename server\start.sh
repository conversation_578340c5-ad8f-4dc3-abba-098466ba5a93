#!/bin/bash

# 次席管理平台 服务器启动脚本

echo "🚀 启动 次席管理平台 服务器..."

# 检查Node.js版本
NODE_VERSION=$(node --version 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js 16+ 版本"
    exit 1
fi

echo "✅ Node.js 版本: $NODE_VERSION"

# 进入服务器目录
cd "$(dirname "$0")"

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 创建数据目录
mkdir -p data
echo "📁 数据目录已准备: $(pwd)/data"

# 设置环境变量
export NODE_ENV=${NODE_ENV:-production}
export PORT=${PORT:-3001}

# 创建日志目录
mkdir -p logs
LOG_DIR="$(pwd)/logs"
ACCESS_LOG="$LOG_DIR/access.log"
ERROR_LOG="$LOG_DIR/error.log"
APP_LOG="$LOG_DIR/app.log"

echo "🔧 环境配置:"
echo "   - 环境: $NODE_ENV"
echo "   - 端口: $PORT"
echo "   - 数据目录: $(pwd)/data"
echo "   - 日志目录: $LOG_DIR"

# 解析命令行参数
DAEMON_MODE=false
FORCE_RESTART=false
SHOW_HELP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--daemon)
            DAEMON_MODE=true
            shift
            ;;
        -f|--force)
            FORCE_RESTART=true
            shift
            ;;
        -h|--help)
            SHOW_HELP=true
            shift
            ;;
        *)
            echo "❌ 未知参数: $1"
            SHOW_HELP=true
            shift
            ;;
    esac
done

# 显示帮助信息
if [ "$SHOW_HELP" = true ]; then
    echo ""
    echo "📖 使用方法:"
    echo "   $0 [选项]"
    echo ""
    echo "选项:"
    echo "   -d, --daemon     后台运行模式"
    echo "   -f, --force      强制重启 (停止现有进程)"
    echo "   -h, --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "   $0                # 前台运行"
    echo "   $0 -d             # 后台运行"
    echo "   $0 -d -f          # 强制重启并后台运行"
    echo ""
    exit 0
fi

# PID 文件路径
PID_FILE="$LOG_DIR/server.pid"

# 检查是否已有进程在运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0  # 进程正在运行
        else
            rm -f "$PID_FILE"  # 清理无效的PID文件
            return 1  # 进程不在运行
        fi
    fi
    return 1  # PID文件不存在
}

# 停止现有进程
stop_server() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        echo "🛑 停止现有服务器进程 (PID: $PID)..."

        # 尝试优雅停止
        kill -TERM "$PID" 2>/dev/null

        # 等待进程停止
        for i in {1..10}; do
            if ! ps -p "$PID" > /dev/null 2>&1; then
                echo "✅ 服务器已停止"
                rm -f "$PID_FILE"
                return 0
            fi
            sleep 1
        done

        # 强制停止
        echo "⚠️  强制停止进程..."
        kill -KILL "$PID" 2>/dev/null
        rm -f "$PID_FILE"
        sleep 2
    fi
}

# 检查现有进程
if check_running; then
    PID=$(cat "$PID_FILE")
    if [ "$FORCE_RESTART" = true ]; then
        echo "🔄 强制重启模式，停止现有进程..."
        stop_server
    else
        echo "⚠️  服务器已在运行 (PID: $PID)"
        echo "   使用 -f 参数强制重启，或手动停止进程"
        echo "   停止命令: kill $PID"
        exit 1
    fi
fi

# 启动服务器
echo "🌟 启动服务器..."

if [ "$DAEMON_MODE" = true ]; then
    echo "🔧 后台运行模式"
    echo "   - 访问日志: $ACCESS_LOG"
    echo "   - 错误日志: $ERROR_LOG"
    echo "   - 应用日志: $APP_LOG"
    echo "   - PID 文件: $PID_FILE"

    # 后台启动
    if [ "$NODE_ENV" = "development" ]; then
        nohup npm run dev > "$APP_LOG" 2>&1 &
    else
        nohup npm start > "$APP_LOG" 2>&1 &
    fi

    # 保存PID
    SERVER_PID=$!
    echo $SERVER_PID > "$PID_FILE"

    # 等待服务器启动
    echo "⏳ 等待服务器启动..."
    sleep 3

    # 检查服务器是否成功启动
    if ps -p "$SERVER_PID" > /dev/null 2>&1; then
        echo "✅ 服务器已在后台启动"
        echo "   - PID: $SERVER_PID"
        echo "   - 端口: $PORT"
        echo "   - 日志: tail -f $APP_LOG"
        echo ""
        echo "🔍 管理命令:"
        echo "   查看日志: tail -f $APP_LOG"
        echo "   查看状态: ps -p $SERVER_PID"
        echo "   停止服务: kill $SERVER_PID"
        echo "   重启服务: $0 -f -d"
    else
        echo "❌ 服务器启动失败"
        rm -f "$PID_FILE"
        echo "📋 查看日志: cat $APP_LOG"
        exit 1
    fi
else
    echo "🔧 前台运行模式"
    echo "   按 Ctrl+C 停止服务器"
    echo ""

    # 前台启动
    if [ "$NODE_ENV" = "development" ]; then
        npm run dev
    else
        npm start
    fi
fi
