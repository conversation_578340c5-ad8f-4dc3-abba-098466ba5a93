#!/bin/bash

# CJFCO Nav 服务器启动脚本

echo "🚀 启动 CJFCO Nav 服务器..."

# 检查Node.js版本
NODE_VERSION=$(node --version 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js 16+ 版本"
    exit 1
fi

echo "✅ Node.js 版本: $NODE_VERSION"

# 进入服务器目录
cd "$(dirname "$0")"

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 创建数据目录
mkdir -p data
echo "📁 数据目录已准备: $(pwd)/data"

# 设置环境变量
export NODE_ENV=${NODE_ENV:-production}
export PORT=${PORT:-3001}

echo "🔧 环境配置:"
echo "   - 环境: $NODE_ENV"
echo "   - 端口: $PORT"
echo "   - 数据目录: $(pwd)/data"

# 启动服务器
echo "🌟 启动服务器..."
if [ "$NODE_ENV" = "development" ]; then
    npm run dev
else
    npm start
fi
