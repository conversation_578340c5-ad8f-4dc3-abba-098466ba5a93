import React from 'react';
import { Search, Settings, Grid, List, Star, Shield } from 'lucide-react';
import { useAppStore } from '../store/useAppStore';

export const Header: React.FC = () => {
  const {
    searchQuery,
    setSearchQuery,
    showFavorites,
    setShowFavorites,
    isAdminMode,
    setAdminMode,
    viewMode,
    setViewMode,
  } = useAppStore();

  return (
    <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-lg overflow-hidden flex items-center justify-center bg-white border border-gray-200">
              <img
                src="/logo.png"
                alt="FinTech Nav Logo"
                className="w-8 h-8 object-contain"
                onError={(e) => {
                  // 如果图片加载失败，显示默认图标
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <Shield className="w-6 h-6 text-blue-600 hidden" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">FinTech Nav</h1>
              <p className="text-sm text-gray-500">金融交易导航</p>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-lg mx-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="搜索应用、描述或标签..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            {/* Favorites Toggle */}
            <button
              onClick={() => setShowFavorites(!showFavorites)}
              className={`p-2 rounded-lg transition-colors ${
                showFavorites
                  ? 'bg-yellow-100 text-yellow-600'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <Star className="w-5 h-5" />
            </button>

            {/* Admin Mode - 简化版本，不显示用户信息 */}
            <button
              onClick={() => setAdminMode(!isAdminMode)}
              className={`p-2 rounded-lg transition-colors ${
                isAdminMode
                  ? 'bg-red-100 text-red-600'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title={isAdminMode ? '退出管理模式' : '进入管理模式'}
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};