import React, { useState } from 'react';
import { Search, Settings, Grid, List, Star, Shield, Palette } from 'lucide-react';
import { useAppStore } from '../store/useAppStore';
import { ThemeToggle } from './ThemeToggle';

export const Header: React.FC = () => {
  const [showThemeToggle, setShowThemeToggle] = useState(false);

  const {
    searchQuery,
    setSearchQuery,
    showFavorites,
    setShowFavorites,
    isAdminMode,
    setAdminMode,
    viewMode,
    setViewMode,
  } = useAppStore();

  return (
    <header className="nav-tech sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-xl overflow-hidden flex items-center justify-center bg-gradient-to-br from-sky-400 to-blue-600 shadow-lg">
              <img
                src="/logo.png"
                alt="CJFCO Nav Logo"
                className="w-8 h-8 object-contain"
                onError={(e) => {
                  // 如果图片加载失败，显示默认图标
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <Shield className="w-6 h-6 text-white hidden" />
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-sky-600 to-blue-600 bg-clip-text text-transparent">CJFCO Nav</h1>
              <p className="text-sm text-slate-600 font-medium">长江期货导航</p>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-lg mx-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="搜索应用、描述或标签..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-tech w-full pl-10 pr-4"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-slate-100/80 rounded-xl p-1 backdrop-blur-sm">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-all ${
                  viewMode === 'grid'
                    ? 'bg-gradient-to-r from-sky-500 to-blue-600 text-white shadow-lg'
                    : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-all ${
                  viewMode === 'list'
                    ? 'bg-gradient-to-r from-sky-500 to-blue-600 text-white shadow-lg'
                    : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            {/* Favorites Toggle */}
            <button
              onClick={() => setShowFavorites(!showFavorites)}
              className={`p-2 rounded-lg transition-colors ${
                showFavorites
                  ? 'bg-yellow-100 text-yellow-600'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <Star className="w-5 h-5" />
            </button>

            {/* Theme Toggle */}
            <button
              onClick={() => setShowThemeToggle(true)}
              className="p-2 rounded-lg transition-all text-slate-600 hover:bg-slate-100 hover:text-slate-900"
              title="主题设置"
            >
              <Palette className="w-5 h-5" />
            </button>

            {/* Admin Mode - 简化版本，不显示用户信息 */}
            <button
              onClick={() => setAdminMode(!isAdminMode)}
              className={`p-2 rounded-lg transition-all ${
                isAdminMode
                  ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg'
                  : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'
              }`}
              title={isAdminMode ? '退出管理模式' : '进入管理模式'}
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Theme Toggle Modal */}
      <ThemeToggle
        isOpen={showThemeToggle}
        onClose={() => setShowThemeToggle(false)}
      />
    </header>
  );
};