import React, { useState, useEffect } from 'react';
import { Palette, Check } from 'lucide-react';

interface Theme {
  id: string;
  name: string;
  description: string;
  primaryColor: string;
  secondaryColor: string;
  preview: {
    background: string;
    card: string;
    text: string;
  };
}

const themes: Theme[] = [
  {
    id: 'tech-blue',
    name: '科技蓝',
    description: '浅蓝色科技感主题',
    primaryColor: '#0EA5E9',
    secondaryColor: '#38BDF8',
    preview: {
      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
      card: '#ffffff',
      text: '#1e293b',
    },
  },
  {
    id: 'classic-blue',
    name: '经典蓝',
    description: '传统蓝色主题',
    primaryColor: '#3B82F6',
    secondaryColor: '#8B5CF6',
    preview: {
      background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
      card: '#ffffff',
      text: '#1e293b',
    },
  },
  {
    id: 'emerald',
    name: '翡翠绿',
    description: '清新绿色主题',
    primaryColor: '#10B981',
    secondaryColor: '#34D399',
    preview: {
      background: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%)',
      card: '#ffffff',
      text: '#1e293b',
    },
  },
  {
    id: 'purple',
    name: '紫罗兰',
    description: '优雅紫色主题',
    primaryColor: '#8B5CF6',
    secondaryColor: '#A78BFA',
    preview: {
      background: 'linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%)',
      card: '#ffffff',
      text: '#1e293b',
    },
  },
];

interface ThemeToggleProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ isOpen, onClose }) => {
  const [currentTheme, setCurrentTheme] = useState('tech-blue');

  useEffect(() => {
    // 从localStorage读取当前主题
    const savedTheme = localStorage.getItem('app-theme');
    if (savedTheme) {
      setCurrentTheme(savedTheme);
    }
  }, []);

  const applyTheme = (theme: Theme) => {
    const root = document.documentElement;
    
    // 更新CSS变量
    root.style.setProperty('--primary-500', theme.primaryColor);
    root.style.setProperty('--secondary-500', theme.secondaryColor);
    
    // 根据主题调整其他颜色
    if (theme.id === 'tech-blue') {
      root.style.setProperty('--primary-50', '#f0f9ff');
      root.style.setProperty('--primary-100', '#e0f2fe');
      root.style.setProperty('--primary-400', '#38bdf8');
      root.style.setProperty('--primary-600', '#0284c7');
    } else if (theme.id === 'emerald') {
      root.style.setProperty('--primary-50', '#f0fdf4');
      root.style.setProperty('--primary-100', '#dcfce7');
      root.style.setProperty('--primary-400', '#4ade80');
      root.style.setProperty('--primary-600', '#059669');
    } else if (theme.id === 'purple') {
      root.style.setProperty('--primary-50', '#faf5ff');
      root.style.setProperty('--primary-100', '#f3e8ff');
      root.style.setProperty('--primary-400', '#a78bfa');
      root.style.setProperty('--primary-600', '#7c3aed');
    } else {
      // classic-blue
      root.style.setProperty('--primary-50', '#eff6ff');
      root.style.setProperty('--primary-100', '#dbeafe');
      root.style.setProperty('--primary-400', '#60a5fa');
      root.style.setProperty('--primary-600', '#2563eb');
    }
    
    // 保存到localStorage
    localStorage.setItem('app-theme', theme.id);
    setCurrentTheme(theme.id);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-sky-400 to-blue-600 rounded-lg flex items-center justify-center">
              <Palette className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">主题设置</h2>
              <p className="text-sm text-gray-500">选择您喜欢的界面主题</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Theme Grid */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {themes.map((theme) => (
              <div
                key={theme.id}
                onClick={() => applyTheme(theme)}
                className={`relative p-4 rounded-xl border-2 cursor-pointer transition-all hover:shadow-lg ${
                  currentTheme === theme.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {/* 选中标识 */}
                {currentTheme === theme.id && (
                  <div className="absolute top-3 right-3 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                )}

                {/* 主题预览 */}
                <div 
                  className="w-full h-20 rounded-lg mb-3 relative overflow-hidden"
                  style={{ background: theme.preview.background }}
                >
                  <div className="absolute top-2 left-2 w-8 h-8 rounded-lg shadow-sm" style={{ backgroundColor: theme.preview.card }}>
                    <div className="w-full h-2 rounded-t-lg" style={{ backgroundColor: theme.primaryColor }}></div>
                  </div>
                  <div className="absolute bottom-2 right-2 flex space-x-1">
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: theme.primaryColor }}></div>
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: theme.secondaryColor }}></div>
                  </div>
                </div>

                {/* 主题信息 */}
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">{theme.name}</h3>
                  <p className="text-sm text-gray-600">{theme.description}</p>
                  <div className="flex items-center space-x-2 mt-2">
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: theme.primaryColor }}
                    ></div>
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: theme.secondaryColor }}
                    ></div>
                    <span className="text-xs text-gray-500">主色调</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              主题设置会自动保存到本地存储
            </p>
            <button
              onClick={onClose}
              className="btn-tech"
            >
              完成
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
