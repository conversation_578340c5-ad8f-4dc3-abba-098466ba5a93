# CJFCO Nav 开发环境启动指南

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

#### Linux/macOS
```bash
# 进入服务器目录
cd server

# 给脚本执行权限
chmod +x dev-start.sh

# 启动开发服务器
./dev-start.sh
```

#### Windows
```cmd
# 进入服务器目录
cd server

# 启动开发服务器
dev-start.bat
```

### 方法二：使用 npm 命令

```bash
# 进入服务器目录
cd server

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm run dev

# 或者启动调试模式
npm run dev:debug
```

## 🔧 开发环境特性

### 自动重载
- ✅ **文件监听**: 监听 `src/` 目录和 `index.js` 文件变化
- ✅ **自动重启**: 文件修改后自动重启服务器
- ✅ **快速反馈**: 无需手动重启，提高开发效率

### 调试支持
- 🐛 **详细错误信息**: 显示完整的错误堆栈
- 🔍 **调试端口**: 使用 `npm run dev:debug` 启用调试端口
- 📊 **开发日志**: 详细的请求和响应日志

### CORS 配置
- 🌐 **宽松策略**: 开发环境允许所有来源访问
- 🔄 **热重载支持**: 支持前端开发服务器的热重载
- 🛠️ **调试友好**: 便于前后端联调

## 📍 服务地址

启动成功后，可以访问以下地址：

- **主服务**: http://localhost:3001
- **健康检查**: http://localhost:3001/health
- **API 接口**: http://localhost:3001/api
- **调试端口**: http://localhost:9229 (使用 dev:debug 时)

## 🔍 API 测试

### 使用 curl 测试

```bash
# 健康检查
curl http://localhost:3001/health

# 获取导航项目
curl http://localhost:3001/api/nav-items

# 获取分类
curl http://localhost:3001/api/categories

# 获取统计数据
curl http://localhost:3001/api/stats

# 获取服务器状态
curl http://localhost:3001/api/status
```

### 使用浏览器测试

直接在浏览器中访问：
- http://localhost:3001/health
- http://localhost:3001/api/nav-items
- http://localhost:3001/api/categories

## 📁 开发环境目录结构

```
server/
├── dev-start.sh/.bat     # 开发启动脚本
├── .env.development      # 开发环境配置
├── package.json          # 项目配置
├── index.js              # 服务器入口
├── data/                 # 开发数据目录
│   ├── navItems.json     # 导航项目数据
│   ├── categories.json   # 分类数据
│   ├── stats.json        # 统计数据
│   └── config.json       # 配置数据
├── logs/                 # 开发日志目录
│   └── dev.log           # 开发日志文件
└── src/
    ├── dataManager.js    # 数据管理器
    └── routes/
        └── api.js        # API 路由
```

## 🛠️ 开发工具

### 推荐的开发工具

1. **API 测试工具**
   - Postman
   - Insomnia
   - VS Code REST Client 插件

2. **调试工具**
   - Chrome DevTools (Node.js 调试)
   - VS Code 调试器
   - Node.js Inspector

3. **日志查看**
   ```bash
   # 实时查看日志
   tail -f logs/dev.log
   
   # Windows
   Get-Content logs/dev.log -Wait
   ```

### VS Code 调试配置

在 `.vscode/launch.json` 中添加：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Server",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/server/index.js",
      "env": {
        "NODE_ENV": "development",
        "PORT": "3001"
      },
      "console": "integratedTerminal",
      "restart": true,
      "runtimeArgs": ["--inspect"]
    }
  ]
}
```

## 🔧 常见问题

### 端口被占用
```bash
# 查看端口占用
lsof -i :3001  # macOS/Linux
netstat -ano | findstr :3001  # Windows

# 终止占用进程
kill -9 <PID>  # macOS/Linux
taskkill /F /PID <PID>  # Windows
```

### 依赖安装失败
```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm install

# 或使用 yarn
yarn install
```

### 数据文件损坏
```bash
# 删除数据文件，重新生成默认数据
rm -rf data/*
# 重启服务器会自动生成默认数据
```

## 📊 性能监控

### 内存使用监控
开发服务器会每分钟输出内存使用情况：
```
内存使用: { rss: 45MB, heapUsed: 23MB }
```

### 请求日志
所有API请求都会记录到控制台和日志文件中。

## 🔄 热重载说明

开发环境使用 `nodemon` 实现热重载：

- **监听文件**: `src/**/*.js`, `index.js`
- **忽略文件**: `node_modules/`, `data/`, `logs/`
- **重启延迟**: 1秒
- **重启提示**: 控制台会显示重启信息

## 🚀 与前端联调

1. **启动后端服务器**:
   ```bash
   cd server
   ./dev-start.sh  # 或 dev-start.bat
   ```

2. **启动前端开发服务器**:
   ```bash
   npm run dev
   ```

3. **验证连接**:
   - 前端: http://localhost:5173
   - 后端: http://localhost:3001
   - API: http://localhost:3001/api

现在您可以开始愉快的开发了！🎉
