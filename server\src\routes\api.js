import express from 'express';

/**
 * 创建API路由
 */
export function createApiRoutes(dataManager) {
  const router = express.Router();

  // ==================== 导航项目相关 ====================

  /**
   * 获取所有导航项目
   */
  router.get('/nav-items', async (req, res) => {
    try {
      const items = dataManager.getNavItems();
      res.json({
        success: true,
        data: items,
        total: items.length
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 添加导航项目
   */
  router.post('/nav-items', async (req, res) => {
    try {
      const newItem = dataManager.addNavItem(req.body);
      res.status(201).json({
        success: true,
        data: newItem
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 更新导航项目
   */
  router.put('/nav-items/:id', async (req, res) => {
    try {
      const updatedItem = dataManager.updateNavItem(req.params.id, req.body);
      res.json({
        success: true,
        data: updatedItem
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 删除导航项目
   */
  router.delete('/nav-items/:id', async (req, res) => {
    try {
      const deletedItem = dataManager.deleteNavItem(req.params.id);
      res.json({
        success: true,
        data: deletedItem
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 增加访问次数
   */
  router.post('/nav-items/:id/access', async (req, res) => {
    try {
      const item = dataManager.incrementAccessCount(req.params.id);
      res.json({
        success: true,
        data: item
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });

  // ==================== 分类相关 ====================

  /**
   * 获取所有分类
   */
  router.get('/categories', async (req, res) => {
    try {
      const categories = dataManager.getCategories();
      res.json({
        success: true,
        data: categories,
        total: categories.length
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 添加分类
   */
  router.post('/categories', async (req, res) => {
    try {
      const newCategory = dataManager.addCategory(req.body);
      res.status(201).json({
        success: true,
        data: newCategory
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 更新分类
   */
  router.put('/categories/:id', async (req, res) => {
    try {
      const updatedCategory = dataManager.updateCategory(req.params.id, req.body);
      res.json({
        success: true,
        data: updatedCategory
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 删除分类
   */
  router.delete('/categories/:id', async (req, res) => {
    try {
      const deletedCategory = dataManager.deleteCategory(req.params.id);
      res.json({
        success: true,
        data: deletedCategory
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  // ==================== 统计数据相关 ====================

  /**
   * 获取统计数据
   */
  router.get('/stats', async (req, res) => {
    try {
      const stats = dataManager.getStats();
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // ==================== 配置相关 ====================

  /**
   * 获取配置
   */
  router.get('/config', async (req, res) => {
    try {
      const config = dataManager.getConfig();
      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 更新配置
   */
  router.put('/config', async (req, res) => {
    try {
      const updatedConfig = dataManager.updateConfig(req.body);
      res.json({
        success: true,
        data: updatedConfig
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  // ==================== 数据管理相关 ====================

  /**
   * 手动保存数据
   */
  router.post('/save', async (req, res) => {
    try {
      await dataManager.saveData();
      res.json({
        success: true,
        message: '数据保存成功'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * 获取服务器状态
   */
  router.get('/status', async (req, res) => {
    try {
      const stats = dataManager.getStats();
      res.json({
        success: true,
        data: {
          server: {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: '1.0.0'
          },
          data: {
            totalItems: stats.totalItems,
            totalCategories: stats.totalCategories,
            totalViews: stats.totalViews,
            lastSaved: dataManager.cache.lastSaved,
            storagePath: dataManager.getStoragePath()
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  return router;
}
