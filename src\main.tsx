import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { configManager } from './utils/config';

// 初始化配置
configManager.loadConfig().then(() => {
  console.log('Configuration loaded successfully');
}).catch((error) => {
  console.warn('Failed to load configuration:', error);
});

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
