import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import { configManager } from './utils/config';
import { useAppStore } from './store/useAppStore';
import './utils/debugStorage';

// 初始化配置
configManager.loadConfig().then(() => {
  console.log('Configuration loaded successfully');
}).catch((error) => {
  console.warn('Failed to load configuration:', error);
});

// 初始化应用数据
const initializeApp = () => {
  const store = useAppStore.getState();
  store.initializeWithMockData();
};

// 延迟初始化，确保 Zustand persist 已经恢复数据
setTimeout(initializeApp, 100);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
