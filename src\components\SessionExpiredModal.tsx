import React, { useState, useEffect } from 'react';
import { AlertTriangle, Clock, RefreshCw, LogOut } from 'lucide-react';

interface SessionExpiredModalProps {
  isOpen: boolean;
  onExtend: () => void;
  onLogout: () => void;
  remainingTime?: number;
  isWarning?: boolean;
}

export const SessionExpiredModal: React.FC<SessionExpiredModalProps> = ({
  isOpen,
  onExtend,
  onLogout,
  remainingTime = 0,
  isWarning = false,
}) => {
  const [countdown, setCountdown] = useState(remainingTime);

  useEffect(() => {
    if (isWarning && remainingTime > 0) {
      setCountdown(remainingTime);
      
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 60000); // 每分钟更新一次

      return () => clearInterval(timer);
    }
  }, [isWarning, remainingTime]);

  if (!isOpen) return null;

  const formatTime = (minutes: number): string => {
    if (minutes <= 0) return '0分钟';
    if (minutes === 1) return '1分钟';
    return `${minutes}分钟`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        <div className="flex items-center space-x-3 mb-4">
          {isWarning ? (
            <div className="flex-shrink-0 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          ) : (
            <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {isWarning ? '会话即将过期' : '会话已过期'}
            </h3>
            <p className="text-sm text-gray-500">
              {isWarning ? '您的登录会话即将到期' : '您的登录会话已到期'}
            </p>
          </div>
        </div>

        <div className="mb-6">
          {isWarning ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="w-4 h-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">
                  剩余时间: {formatTime(countdown)}
                </span>
              </div>
              <p className="text-sm text-yellow-700">
                为了保护您的账户安全，系统将在 {formatTime(countdown)} 后自动登出。
                您可以选择延长会话或立即重新登录。
              </p>
            </div>
          ) : (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-700">
                由于长时间未操作，您的会话已过期。请重新登录以继续使用系统。
              </p>
            </div>
          )}
        </div>

        <div className="flex space-x-3">
          {isWarning ? (
            <>
              <button
                onClick={onExtend}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
              >
                <RefreshCw className="w-4 h-4" />
                <span>延长会话</span>
              </button>
              <button
                onClick={onLogout}
                className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>立即登出</span>
              </button>
            </>
          ) : (
            <button
              onClick={onLogout}
              className="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center space-x-2"
            >
              <LogOut className="w-4 h-4" />
              <span>重新登录</span>
            </button>
          )}
        </div>

        {isWarning && (
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              系统会在您进行任何操作时自动延长会话时间
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
