import { create } from 'zustand';
import { AppState, NavItem, Category, User, Stats } from '../types';
import { dataSyncService } from '../services/apiClient';
import { ipManager } from '../utils/ipManager';

interface ApiStore extends AppState {
  // Loading states
  isLoading: boolean;
  isOnline: boolean;
  error: string | null;
  
  // Actions
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: string) => void;
  setShowFavorites: (show: boolean) => void;
  setViewMode: (mode: 'grid' | 'list') => void;
  setAdminMode: (isAdmin: boolean) => void;
  
  // Data loading
  loadData: () => Promise<void>;
  checkOnlineStatus: () => Promise<boolean>;
  
  // Nav Items
  addNavItem: (item: Omit<NavItem, 'id' | 'createdAt' | 'updatedAt' | 'accessCount'>) => Promise<void>;
  updateNavItem: (id: string, updates: Partial<NavItem>) => Promise<void>;
  deleteNavItem: (id: string) => Promise<void>;
  incrementAccessCount: (id: string) => Promise<void>;
  toggleLike: (id: string) => void;
  toggleFavorite: (id: string) => void;
  
  // Categories
  addCategory: (category: Omit<Category, 'id' | 'itemCount'>) => Promise<void>;
  updateCategory: (id: string, updates: Partial<Category>) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;
  
  // User
  setCurrentUser: (user: User | null) => void;

  // Computed
  getFilteredItems: () => NavItem[];
  getStats: () => Stats;
  getCategoryStats: () => Category[];
  hasLikedItem: (itemId: string) => boolean;
}

export const useApiStore = create<ApiStore>((set, get) => ({
  // Initial state
  items: [],
  categories: [],
  currentUser: null,
  searchQuery: '',
  selectedCategory: 'all',
  showFavorites: false,
  viewMode: 'grid',
  isAdminMode: false,
  isLoading: false,
  isOnline: false,
  error: null,

  // Basic setters
  setSearchQuery: (query: string) => set({ searchQuery: query }),
  setSelectedCategory: (category: string) => set({ selectedCategory: category }),
  setShowFavorites: (show: boolean) => set({ showFavorites: show }),
  setViewMode: (mode: 'grid' | 'list') => set({ viewMode: mode }),
  setAdminMode: (isAdmin: boolean) => set({ isAdminMode: isAdmin }),
  setCurrentUser: (user: User | null) => set({ currentUser: user }),

  // Check online status
  checkOnlineStatus: async () => {
    try {
      const isOnline = await dataSyncService.checkOnlineStatus();
      set({ isOnline });
      return isOnline;
    } catch (error) {
      set({ isOnline: false });
      return false;
    }
  },

  // Load data from API
  loadData: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // Check if server is online
      const isOnline = await dataSyncService.checkOnlineStatus();
      set({ isOnline });

      if (isOnline) {
        console.log('🌐 从服务器加载数据...');
        
        // Load data from server
        const [items, categories] = await Promise.all([
          dataSyncService.getNavItems(),
          dataSyncService.getCategories()
        ]);

        set({ 
          items, 
          categories,
          isLoading: false,
          error: null 
        });

        console.log(`✅ 数据加载成功: ${items.length} 个应用, ${categories.length} 个分类`);
      } else {
        console.log('📱 服务器离线，使用本地数据...');
        
        // Fallback to local data
        const localItems = localStorage.getItem('navItems');
        const localCategories = localStorage.getItem('categories');
        
        set({
          items: localItems ? JSON.parse(localItems) : [],
          categories: localCategories ? JSON.parse(localCategories) : [
            {
              id: 'all',
              name: '全部应用',
              color: '#6B7280',
              icon: 'Grid',
              description: '显示所有应用',
              itemCount: 0,
              isSystem: true
            },
            {
              id: 'favorites',
              name: '收藏夹',
              color: '#F59E0B',
              icon: 'Star',
              description: '收藏的应用',
              itemCount: 0,
              isSystem: true
            }
          ],
          isLoading: false,
          error: '服务器离线，使用本地数据'
        });
      }
    } catch (error) {
      console.error('❌ 数据加载失败:', error);
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '数据加载失败' 
      });
    }
  },

  // Nav Items operations
  addNavItem: async (item) => {
    set({ isLoading: true });
    
    try {
      const newItem = await dataSyncService.addNavItem(item);
      
      set(state => ({
        items: [...state.items, newItem],
        isLoading: false
      }));
      
      console.log('✅ 应用添加成功:', newItem.name);
    } catch (error) {
      console.error('❌ 应用添加失败:', error);
      set({ 
        isLoading: false,
        error: error instanceof Error ? error.message : '应用添加失败'
      });
    }
  },

  updateNavItem: async (id, updates) => {
    set({ isLoading: true });
    
    try {
      const updatedItem = await dataSyncService.updateNavItem(id, updates);
      
      set(state => ({
        items: state.items.map(item => 
          item.id === id ? updatedItem : item
        ),
        isLoading: false
      }));
      
      console.log('✅ 应用更新成功:', updatedItem.name);
    } catch (error) {
      console.error('❌ 应用更新失败:', error);
      set({ 
        isLoading: false,
        error: error instanceof Error ? error.message : '应用更新失败'
      });
    }
  },

  deleteNavItem: async (id) => {
    set({ isLoading: true });
    
    try {
      await dataSyncService.deleteNavItem(id);
      
      set(state => ({
        items: state.items.filter(item => item.id !== id),
        isLoading: false
      }));
      
      console.log('✅ 应用删除成功');
    } catch (error) {
      console.error('❌ 应用删除失败:', error);
      set({ 
        isLoading: false,
        error: error instanceof Error ? error.message : '应用删除失败'
      });
    }
  },

  incrementAccessCount: async (id) => {
    try {
      const updatedItem = await dataSyncService.incrementAccessCount(id);
      
      set(state => ({
        items: state.items.map(item => 
          item.id === id ? updatedItem : item
        )
      }));
    } catch (error) {
      console.error('❌ 访问次数更新失败:', error);
    }
  },

  // Categories operations
  addCategory: async (category) => {
    set({ isLoading: true });
    
    try {
      const newCategory = await dataSyncService.addCategory(category);
      
      set(state => ({
        categories: [...state.categories, newCategory],
        isLoading: false
      }));
      
      console.log('✅ 分类添加成功:', newCategory.name);
    } catch (error) {
      console.error('❌ 分类添加失败:', error);
      set({ 
        isLoading: false,
        error: error instanceof Error ? error.message : '分类添加失败'
      });
    }
  },

  updateCategory: async (id, updates) => {
    set({ isLoading: true });
    
    try {
      const updatedCategory = await dataSyncService.updateCategory(id, updates);
      
      set(state => ({
        categories: state.categories.map(cat => 
          cat.id === id ? updatedCategory : cat
        ),
        isLoading: false
      }));
      
      console.log('✅ 分类更新成功:', updatedCategory.name);
    } catch (error) {
      console.error('❌ 分类更新失败:', error);
      set({ 
        isLoading: false,
        error: error instanceof Error ? error.message : '分类更新失败'
      });
    }
  },

  deleteCategory: async (id) => {
    set({ isLoading: true });
    
    try {
      await dataSyncService.deleteCategory(id);
      
      set(state => ({
        categories: state.categories.filter(cat => cat.id !== id),
        isLoading: false
      }));
      
      console.log('✅ 分类删除成功');
    } catch (error) {
      console.error('❌ 分类删除失败:', error);
      set({ 
        isLoading: false,
        error: error instanceof Error ? error.message : '分类删除失败'
      });
    }
  },

  // Local operations (no API call needed)
  toggleLike: (id: string) => {
    const currentIp = ipManager.getCurrentIP();
    const hasLiked = ipManager.hasLikedItem(id);
    
    if (hasLiked) {
      ipManager.removeLike(id);
    } else {
      ipManager.addLike(id);
    }
    
    set(state => ({
      items: state.items.map(item => {
        if (item.id === id) {
          const newLikes = hasLiked 
            ? (item.likes || []).filter(ip => ip !== currentIp)
            : [...(item.likes || []), currentIp];
          return { ...item, likes: newLikes };
        }
        return item;
      })
    }));
  },

  toggleFavorite: (id: string) => {
    const currentIp = ipManager.getCurrentIP();
    
    set(state => ({
      items: state.items.map(item => {
        if (item.id === id) {
          const isFavorited = item.favoritedBy?.includes(currentIp);
          const newFavoritedBy = isFavorited
            ? (item.favoritedBy || []).filter(ip => ip !== currentIp)
            : [...(item.favoritedBy || []), currentIp];
          return { ...item, favoritedBy: newFavoritedBy };
        }
        return item;
      })
    }));
  },

  // Computed values
  getFilteredItems: () => {
    const state = get();
    let filtered = state.items;

    // Filter by category
    if (state.selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === state.selectedCategory);
    }

    // Filter by favorites
    if (state.showFavorites) {
      const currentIp = ipManager.getCurrentIP();
      filtered = filtered.filter(item => 
        item.favoritedBy?.includes(currentIp)
      );
    }

    // Filter by search query
    if (state.searchQuery) {
      const query = state.searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query) ||
        item.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered;
  },

  getStats: () => {
    const state = get();
    return {
      totalItems: state.items.length,
      totalViews: state.items.reduce((sum, item) => sum + (item.accessCount || 0), 0),
      totalCategories: state.categories.filter(cat => !cat.isSystem).length,
      totalLikes: state.items.reduce((sum, item) => sum + (item.likes?.length || 0), 0),
    };
  },

  getCategoryStats: () => {
    const state = get();
    return state.categories.map(category => ({
      ...category,
      itemCount: state.items.filter(item => item.category === category.id).length
    }));
  },

  hasLikedItem: (itemId: string) => {
    return ipManager.hasLikedItem(itemId);
  },
}));
