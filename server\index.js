import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { DataManager } from './src/dataManager.js';
import { createApiRoutes } from './src/routes/api.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;
const isDevelopment = process.env.NODE_ENV === 'development';

// 初始化数据管理器
const dataManager = new DataManager();
await dataManager.initialize();

// 中间件配置
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(compression());
app.use(cors({
  origin: isDevelopment
    ? true  // 开发环境允许所有来源
    : ['http://localhost:5173', 'https://your-domain.com'],
  credentials: true
}));

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP最多1000次请求
  message: {
    error: '请求过于频繁，请稍后再试'
  }
});

app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API路由
app.use('/api', createApiRoutes(dataManager));

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime()
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    error: '服务器内部错误',
    message: isDevelopment ? err.message : '请联系管理员',
    ...(isDevelopment && { stack: err.stack }) // 开发环境显示错误堆栈
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: '接口不存在',
    path: req.path
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 次席管理平台 服务器启动成功`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔧 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`💾 数据存储: ${dataManager.getStoragePath()}`);

  if (isDevelopment) {
    console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
    console.log(`📚 API 接口: http://localhost:${PORT}/api`);
    console.log(`🔄 热重载: 已启用 (nodemon)`);
    console.log(`🐛 调试模式: 已启用`);
  }
});

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('📴 收到关闭信号，正在优雅关闭...');
  await dataManager.saveData();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('📴 收到中断信号，正在优雅关闭...');
  await dataManager.saveData();
  process.exit(0);
});
