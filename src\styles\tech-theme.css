/* 科技感浅蓝色主题 */

:root {
  /* 主色调 - 浅蓝色系 */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* 辅助色 - 青色系 */
  --secondary-50: #ecfeff;
  --secondary-100: #cffafe;
  --secondary-200: #a5f3fc;
  --secondary-300: #67e8f9;
  --secondary-400: #22d3ee;
  --secondary-500: #06b6d4;
  --secondary-600: #0891b2;
  --secondary-700: #0e7490;
  --secondary-800: #155e75;
  --secondary-900: #164e63;

  /* 中性色 - 科技感灰色 */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* 功能色 */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: var(--primary-500);

  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--secondary-600) 100%);
  --gradient-tech: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 50%, var(--primary-600) 100%);

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(14, 165, 233, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(14, 165, 233, 0.1), 0 2px 4px -1px rgba(14, 165, 233, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(14, 165, 233, 0.1), 0 4px 6px -2px rgba(14, 165, 233, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(14, 165, 233, 0.1), 0 10px 10px -5px rgba(14, 165, 233, 0.04);
  --shadow-glow: 0 0 20px rgba(14, 165, 233, 0.3);

  /* 边框半径 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* 动画 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
  color: var(--gray-800);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 科技感按钮样式 */
.btn-tech {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.btn-tech::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn-tech:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-tech:hover::before {
  left: 100%;
}

.btn-tech:active {
  transform: translateY(0);
}

/* 科技感卡片样式 */
.card-tech {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(14, 165, 233, 0.1);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card-tech::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-tech);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card-tech:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: rgba(14, 165, 233, 0.2);
}

.card-tech:hover::before {
  opacity: 1;
}

/* 科技感输入框 */
.input-tech {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all var(--transition-normal);
  position: relative;
}

.input-tech:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  background: white;
}

/* 科技感导航 */
.nav-tech {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(14, 165, 233, 0.1);
  box-shadow: var(--shadow-sm);
}

/* 科技感侧边栏 */
.sidebar-tech {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(14, 165, 233, 0.1);
}

/* 科技感统计卡片 */
.stat-card-tech {
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  color: white;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.stat-card-tech::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

/* 科技感加载动画 */
.loading-tech {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--primary-200);
  border-radius: 50%;
  border-top-color: var(--primary-500);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 科技感滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: var(--radius-sm);
  transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-600);
}

/* 科技感选择状态 */
.selected-tech {
  background: var(--gradient-primary) !important;
  color: white !important;
  box-shadow: var(--shadow-glow);
}

/* 科技感悬停效果 */
.hover-tech {
  transition: all var(--transition-normal);
}

.hover-tech:hover {
  background: rgba(14, 165, 233, 0.05);
  transform: translateX(4px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-tech {
    border-radius: var(--radius-lg);
  }
  
  .btn-tech {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }
}
