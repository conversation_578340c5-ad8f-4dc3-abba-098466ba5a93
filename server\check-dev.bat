@echo off
chcp 65001 >nul

echo 🔍 检查 长江期货导航系统 开发环境状态...
echo.

REM 设置服务器地址
set SERVER_URL=http://localhost:3001

echo 📍 服务器地址: %SERVER_URL%
echo.

REM 检查健康状态
echo 🏥 检查服务器健康状态...
curl -s "%SERVER_URL%/health" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 服务器运行正常
    for /f "delims=" %%i in ('curl -s "%SERVER_URL%/health"') do echo    %%i
) else (
    echo ❌ 服务器未启动或无法访问
    echo 💡 请先运行: dev-start.bat
    goto :end
)

echo.

REM 检查API接口
echo 🔌 检查API接口...

echo 📋 导航项目接口:
curl -s "%SERVER_URL%/api/nav-items" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ /api/nav-items - 正常
) else (
    echo ❌ /api/nav-items - 异常
)

echo 📁 分类接口:
curl -s "%SERVER_URL%/api/categories" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ /api/categories - 正常
) else (
    echo ❌ /api/categories - 异常
)

echo 📊 统计接口:
curl -s "%SERVER_URL%/api/stats" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ /api/stats - 正常
) else (
    echo ❌ /api/stats - 异常
)

echo 🔧 配置接口:
curl -s "%SERVER_URL%/api/config" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ /api/config - 正常
) else (
    echo ❌ /api/config - 异常
)

echo.

REM 检查数据文件
echo 📁 检查数据文件...
if exist "data\navItems.json" (
    echo ✅ navItems.json - 存在
) else (
    echo ❌ navItems.json - 不存在
)

if exist "data\categories.json" (
    echo ✅ categories.json - 存在
) else (
    echo ❌ categories.json - 不存在
)

if exist "data\stats.json" (
    echo ✅ stats.json - 存在
) else (
    echo ❌ stats.json - 不存在
)

if exist "data\config.json" (
    echo ✅ config.json - 存在
) else (
    echo ❌ config.json - 不存在
)

echo.

REM 显示服务器状态
echo 🖥️ 服务器详细状态:
curl -s "%SERVER_URL%/api/status" 2>nul | findstr /C:"totalItems" /C:"totalCategories" /C:"uptime"

echo.

REM 显示可用的开发命令
echo 🛠️ 可用的开发命令:
echo    dev-start.bat     - 启动开发服务器
echo    check-dev.bat     - 检查服务器状态 (当前脚本)
echo    npm run dev       - 直接启动开发模式
echo    npm run dev:debug - 启动调试模式

echo.
echo 🌐 开发地址:
echo    前端开发服务器: http://localhost:5173
echo    后端API服务器:  %SERVER_URL%
echo    健康检查:       %SERVER_URL%/health
echo    API文档:        %SERVER_URL%/api

:end
echo.
pause
