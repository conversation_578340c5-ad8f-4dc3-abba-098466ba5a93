import React from 'react';
import { Header } from './components/Header';
import { Sidebar } from './components/Sidebar';
import { NavItemGrid } from './components/NavItemGrid';
import { SessionExpiredModal } from './components/SessionExpiredModal';
import { useSession } from './hooks/useSession';

function App() {
  const {
    showWarningModal,
    showExpiredModal,
    remainingTime,
    extendSession,
    logout,
    closeWarningModal,
  } = useSession();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 overflow-hidden">
          <NavItemGrid />
        </main>
      </div>

      {/* 会话警告模态框 */}
      <SessionExpiredModal
        isOpen={showWarningModal}
        onExtend={extendSession}
        onLogout={logout}
        remainingTime={remainingTime}
        isWarning={true}
      />

      {/* 会话过期模态框 */}
      <SessionExpiredModal
        isOpen={showExpiredModal}
        onExtend={extendSession}
        onLogout={logout}
        isWarning={false}
      />
    </div>
  );
}

export default App;