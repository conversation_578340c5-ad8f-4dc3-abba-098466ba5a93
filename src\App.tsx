import React from 'react';
import { AppWithApi } from './components/AppWithApi';
import { SessionExpiredModal } from './components/SessionExpiredModal';
import { useSession } from './hooks/useSession';

function App() {
  const {
    showWarningModal,
    showExpiredModal,
    remainingTime,
    extendSession,
    logout,
    closeWarningModal,
  } = useSession();

  return (
    <>
      <AppWithApi />

      {/* 会话警告模态框 */}
      <SessionExpiredModal
        isOpen={showWarningModal}
        onExtend={extendSession}
        onLogout={logout}
        remainingTime={remainingTime}
        isWarning={true}
      />

      {/* 会话过期模态框 */}
      <SessionExpiredModal
        isOpen={showExpiredModal}
        onExtend={extendSession}
        onLogout={logout}
        isWarning={false}
      />
    </>
  );
}

export default App;