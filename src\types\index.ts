export interface NavItem {
  id: string;
  name: string;
  description: string;
  url: string;
  icon: string;
  category: string;
  tags: string[];
  isInternal: boolean;
  isFavorite: boolean;
  accessCount: number;
  likeCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  color: string;
  icon: string;
  description: string;
  itemCount: number;
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: 'admin' | 'user';
  favorites: string[];
}

export interface AppState {
  items: NavItem[];
  categories: Category[];
  currentUser: User | null;
  searchQuery: string;
  selectedCategory: string;
  showFavorites: boolean;
  isAdminMode: boolean;
  viewMode: 'grid' | 'list';
}

export interface Stats {
  totalItems: number;
  totalCategories: number;
  totalViews: number;
  totalLikes: number;
  totalFavorites: number;
  mostPopular: NavItem[];
  recentlyAdded: NavItem[];
}