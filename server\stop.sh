#!/bin/bash

# 长江期货导航系统 服务器停止脚本

echo "🛑 停止 长江期货导航系统 服务器..."

# 进入服务器目录
cd "$(dirname "$0")"

# 日志和PID文件路径
LOG_DIR="$(pwd)/logs"
PID_FILE="$LOG_DIR/server.pid"
APP_LOG="$LOG_DIR/app.log"

# 解析命令行参数
FORCE_STOP=false
SHOW_HELP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--force)
            FORCE_STOP=true
            shift
            ;;
        -h|--help)
            SHOW_HELP=true
            shift
            ;;
        *)
            echo "❌ 未知参数: $1"
            SHOW_HELP=true
            shift
            ;;
    esac
done

# 显示帮助信息
if [ "$SHOW_HELP" = true ]; then
    echo ""
    echo "📖 使用方法:"
    echo "   $0 [选项]"
    echo ""
    echo "选项:"
    echo "   -f, --force      强制停止 (使用 SIGKILL)"
    echo "   -h, --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "   $0               # 优雅停止"
    echo "   $0 -f            # 强制停止"
    echo ""
    exit 0
fi

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "⚠️  未找到PID文件: $PID_FILE"
    echo "   服务器可能未在后台运行"
    
    # 尝试查找进程
    echo "🔍 搜索可能的服务器进程..."
    PIDS=$(pgrep -f "node.*index.js" 2>/dev/null)
    
    if [ -n "$PIDS" ]; then
        echo "📋 找到可能的进程:"
        ps -p $PIDS -o pid,ppid,cmd 2>/dev/null
        echo ""
        echo "💡 如需停止这些进程，请手动执行:"
        for pid in $PIDS; do
            echo "   kill $pid"
        done
    else
        echo "✅ 未找到相关进程"
    fi
    
    exit 1
fi

# 读取PID
PID=$(cat "$PID_FILE")

# 检查进程是否存在
if ! ps -p "$PID" > /dev/null 2>&1; then
    echo "⚠️  进程 $PID 不存在，清理PID文件"
    rm -f "$PID_FILE"
    exit 0
fi

echo "🔍 找到服务器进程 (PID: $PID)"

# 停止进程
if [ "$FORCE_STOP" = true ]; then
    echo "⚡ 强制停止进程..."
    kill -KILL "$PID" 2>/dev/null
    sleep 1
else
    echo "🤝 优雅停止进程..."
    kill -TERM "$PID" 2>/dev/null
    
    # 等待进程停止
    echo "⏳ 等待进程停止..."
    for i in {1..15}; do
        if ! ps -p "$PID" > /dev/null 2>&1; then
            break
        fi
        echo -n "."
        sleep 1
    done
    echo ""
    
    # 检查是否还在运行
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "⚠️  进程未响应，强制停止..."
        kill -KILL "$PID" 2>/dev/null
        sleep 1
    fi
fi

# 最终检查
if ps -p "$PID" > /dev/null 2>&1; then
    echo "❌ 停止失败，进程仍在运行"
    echo "   请手动停止: kill -9 $PID"
    exit 1
else
    echo "✅ 服务器已停止"
    rm -f "$PID_FILE"
    
    # 显示最后几行日志
    if [ -f "$APP_LOG" ]; then
        echo ""
        echo "📋 最后几行日志:"
        tail -5 "$APP_LOG" 2>/dev/null || echo "   (无法读取日志文件)"
    fi
fi

echo ""
echo "🔧 其他管理命令:"
echo "   启动服务: ./start.sh -d"
echo "   查看日志: tail -f $APP_LOG"
echo "   重启服务: ./start.sh -f -d"
