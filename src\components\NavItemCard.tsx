import React from 'react';
import { NavItem } from '../types';
import { useAppStore } from '../store/useAppStore';
import {
  ExternalLink,
  Heart,
  Eye,
  Star,
  Edit,
  Trash2,
  TrendingUp,
  Shield,
  Bar<PERSON>hart3,
  Users,
  Calculator,
  Settings,
} from 'lucide-react';

const iconMap = {
  TrendingUp,
  Shield,
  BarChart3,
  Users,
  Calculator,
  Settings,
};

interface NavItemCardProps {
  item: NavItem;
  viewMode: 'grid' | 'list';
  onEdit?: () => void;
}

export const NavItemCard: React.FC<NavItemCardProps> = ({ item, viewMode, onEdit }) => {
  const {
    incrementAccessCount,
    toggleLike,
    toggleFavorite,
    deleteNavItem,
    isAdminMode,
    categories,
    currentUser,
  } = useAppStore();

  const IconComponent = iconMap[item.icon as keyof typeof iconMap] || Settings;
  const category = categories.find(cat => cat.id === item.category);
  const isFavorite = currentUser?.favorites.includes(item.id);

  const handleClick = () => {
    incrementAccessCount(item.id);
    if (item.isInternal) {
      // For internal apps, you might want to handle routing differently
      window.open(item.url, '_blank');
    } else {
      window.open(item.url, '_blank');
    }
  };

  const handleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleFavorite(item.id);
  };

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleLike(item.id);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.();
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm('确定要删除这个应用吗？此操作无法撤销。')) {
      deleteNavItem(item.id);
    }
  };

  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all duration-300 group">
        <div className="p-4 flex items-center space-x-4">
          {/* Icon */}
          <div 
            className="w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0"
            style={{ backgroundColor: `${category?.color}20` }}
          >
            <IconComponent className="w-6 h-6" style={{ color: category?.color }} />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {item.name}
              </h3>
              {item.isInternal && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                  内部
                </span>
              )}
            </div>
            <p className="text-gray-600 text-sm mt-1 line-clamp-2">
              {item.description}
            </p>
            <div className="flex items-center space-x-4 mt-2">
              <div className="flex items-center space-x-1 text-xs text-gray-500">
                <Eye className="w-3 h-3" />
                <span>{item.accessCount}</span>
              </div>
              <div className="flex items-center space-x-1 text-xs text-gray-500">
                <Heart className="w-3 h-3" />
                <span>{item.likeCount}</span>
              </div>
              <div className="flex items-center space-x-1">
                {item.tags.slice(0, 2).map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-700"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleFavorite}
              className={`p-2 rounded-full transition-colors ${
                isFavorite
                  ? 'bg-yellow-100 text-yellow-600'
                  : 'text-gray-400 hover:bg-gray-100 hover:text-gray-600'
              }`}
            >
              <Star className="w-4 h-4" />
            </button>
            <button
              onClick={handleLike}
              className="p-2 rounded-full text-gray-400 hover:bg-gray-100 hover:text-red-500 transition-colors"
            >
              <Heart className="w-4 h-4" />
            </button>
            <button
              onClick={handleClick}
              className="p-2 rounded-full text-gray-400 hover:bg-blue-100 hover:text-blue-600 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
            </button>
            {isAdminMode && (
              <>
                <button
                  onClick={handleEdit}
                  className="p-2 rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors"
                  title="编辑应用"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={handleDelete}
                  className="p-2 rounded-full text-gray-400 hover:bg-red-100 hover:text-red-600 transition-colors"
                  title="删除应用"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="bg-white rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden"
      onClick={handleClick}
    >
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between">
          <div 
            className="w-12 h-12 rounded-xl flex items-center justify-center mb-4"
            style={{ backgroundColor: `${category?.color}20` }}
          >
            <IconComponent className="w-6 h-6" style={{ color: category?.color }} />
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={handleFavorite}
              className={`p-1.5 rounded-full transition-colors ${
                isFavorite
                  ? 'bg-yellow-100 text-yellow-600'
                  : 'text-gray-400 hover:bg-gray-100 hover:text-gray-600'
              }`}
            >
              <Star className="w-4 h-4" />
            </button>
            {isAdminMode && (
              <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={handleEdit}
                  className="p-1.5 rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors"
                  title="编辑应用"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={handleDelete}
                  className="p-1.5 rounded-full text-gray-400 hover:bg-red-100 hover:text-red-600 transition-colors"
                  title="删除应用"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2 mb-2">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {item.name}
          </h3>
          {item.isInternal && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
              内部
            </span>
          )}
        </div>

        <p className="text-gray-600 text-sm line-clamp-2 mb-4">
          {item.description}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-4">
          {item.tags.slice(0, 3).map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1 text-sm text-gray-500">
              <Eye className="w-4 h-4" />
              <span>{item.accessCount}</span>
            </div>
            <button
              onClick={handleLike}
              className={`flex items-center space-x-1 text-sm transition-colors ${
                item.likeCount > 0 ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
              }`}
            >
              <Heart className="w-4 h-4" />
              <span>{item.likeCount}</span>
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">
              {category?.name}
            </span>
            <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" />
          </div>
        </div>
      </div>
    </div>
  );
};