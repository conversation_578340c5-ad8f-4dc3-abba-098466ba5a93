# FinTradeNav - 金融交易导航系统

一个现代化的企业级导航管理系统，提供统一的系统入口和资源管理功能。

## 🚀 功能特性

- **智能导航管理** - 统一管理企业内部系统和外部资源链接
- **分类管理** - 支持自定义分类，便于组织和查找资源
- **搜索功能** - 强大的全文搜索，支持名称、描述和标签搜索
- **收藏系统** - 用户可以收藏常用资源，快速访问
- **访问统计** - 记录资源访问次数，了解使用情况
- **点赞功能** - 用户可以为有用的资源点赞
- **管理员模式** - 管理员可以添加、编辑和删除资源
- **响应式设计** - 支持桌面端和移动端访问
- **数据持久化** - 使用 Zustand 进行状态管理和本地存储

## 🛠️ 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **状态管理**: Zustand
- **UI 组件**: Lucide React Icons
- **样式框架**: Tailwind CSS
- **代码规范**: ESLint + TypeScript ESLint

## 📦 安装和运行

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

默认访问地址：http://localhost:5173

### 生产环境构建

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## ⚙️ 配置说明

### 端口配置

项目支持通过配置文件自定义端口设置，配置文件位于 `config/app.config.json`：

```json
{
  "server": {
    "port": 5173,
    "host": "localhost"
  },
  "app": {
    "title": "FinTech Nav - 金融交易导航系统",
    "sessionTimeout": 30
  }
}
```

### 环境变量

创建 `.env` 文件来配置环境变量：

```env
VITE_APP_TITLE=FinTech Nav
VITE_SESSION_TIMEOUT=30
VITE_PORT=5173
```

## 📁 项目结构

```
FinTradeNav/
├── src/
│   ├── components/          # React 组件
│   │   ├── Header.tsx      # 头部组件
│   │   ├── Sidebar.tsx     # 侧边栏组件
│   │   └── NavItemGrid.tsx # 导航项网格组件
│   ├── store/              # 状态管理
│   │   └── useAppStore.ts  # 主要状态存储
│   ├── types/              # TypeScript 类型定义
│   │   └── index.ts        # 类型定义文件
│   ├── data/               # 模拟数据
│   │   └── mockData.ts     # 初始数据
│   ├── App.tsx             # 主应用组件
│   ├── main.tsx            # 应用入口
│   └── index.css           # 全局样式
├── config/                 # 配置文件
│   └── app.config.json     # 应用配置
├── public/                 # 静态资源
├── dist/                   # 构建输出
└── README.md               # 项目说明
```

## 🎯 使用指南

### 基本操作

1. **浏览资源** - 在主界面查看所有可用的导航资源
2. **搜索资源** - 使用顶部搜索框快速查找所需资源
3. **分类筛选** - 点击左侧分类标签筛选特定类型的资源
4. **收藏资源** - 点击星形图标将常用资源添加到收藏夹
5. **访问资源** - 点击资源卡片直接跳转到目标系统

### 管理员功能

1. **启用管理模式** - 点击右上角的管理员图标
2. **添加资源** - 在管理模式下添加新的导航资源
3. **编辑资源** - 修改现有资源的信息
4. **删除资源** - 移除不再需要的资源
5. **分类管理** - 创建和管理资源分类

## 🔧 开发指南

### 添加新功能

1. 在 `src/types/index.ts` 中定义相关类型
2. 在 `src/store/useAppStore.ts` 中添加状态和操作方法
3. 创建相应的 React 组件
4. 在主应用中集成新功能

### 自定义样式

项目使用 Tailwind CSS，可以通过修改 `tailwind.config.js` 来自定义主题：

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#your-color',
      },
    },
  },
}
```

## 🚀 部署

### 使用 Nginx

1. 构建项目：`npm run build`
2. 将 `dist` 目录内容复制到 Nginx 服务器
3. 配置 Nginx：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 使用 Docker

```dockerfile
FROM nginx:alpine
COPY dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础导航功能
- 用户管理系统
- 响应式设计

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请联系开发团队。

---

**FinTradeNav** - 让企业资源管理更简单高效 🚀
