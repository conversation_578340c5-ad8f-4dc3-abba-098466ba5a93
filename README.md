# 次席管理平台

一个现代化的企业级导航管理系统，专为金融期货行业设计，提供统一的系统入口和资源管理功能。采用前后端分离架构，支持多用户数据同步和企业级数据管理。

## ✨ 核心特性

### 🎯 导航管理
- **智能分类** - 按业务功能分类：交易系统、风险管理、数据分析、客户服务、办公工具
- **快速搜索** - 支持应用名称、描述、标签的全文搜索
- **收藏系统** - 个人收藏夹，快速访问常用应用
- **访问统计** - 实时统计应用访问次数和热门应用排行

### 🔐 用户体验
- **IP级权限** - 基于IP地址的用户识别和权限管理
- **点赞互动** - 用户可以为有用的应用点赞推荐
- **管理模式** - 管理员可以添加、编辑、删除应用和分类
- **响应式设计** - 完美适配桌面端、平板和移动设备

### 🏗️ 技术架构
- **前后端分离** - React前端 + Node.js后端，支持独立部署
- **数据同步** - 多客户端数据实时同步，解决缓存一致性问题
- **在线/离线** - 支持离线模式，网络恢复后自动同步
- **企业级存储** - 服务器端数据持久化，支持多用户协作

## 🛠️ 技术栈

### 前端技术
- **框架**: React 18 + TypeScript
- **构建工具**: Vite 5.x
- **状态管理**: Zustand (支持API集成)
- **UI组件**: Lucide React Icons
- **样式框架**: Tailwind CSS
- **HTTP客户端**: Fetch API + 自定义封装

### 后端技术
- **运行环境**: Node.js 18+
- **框架**: Express.js
- **数据存储**: JSON文件 + 内存缓存
- **API设计**: RESTful API
- **安全**: CORS + 安全头部
- **进程管理**: Nodemon (开发) / PM2 (生产)

### 开发工具
- **代码规范**: ESLint + TypeScript ESLint
- **版本控制**: Git
- **包管理**: npm
- **API测试**: curl / Postman

## � 快速开始

### 环境要求

- **Node.js**: >= 18.0.0
- **npm**: >= 9.0.0
- **操作系统**: Windows / macOS / Linux

### 1. 克隆项目

```bash
git clone <repository-url>
cd FinTradeNav
```

### 2. 安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd server
npm install
cd ..
```

### 3. 启动服务

```bash
# 启动后端服务 (终端1)
cd server
npm run dev
# 后端服务: http://localhost:3001

# 启动前端服务 (终端2)
npm run dev
# 前端服务: http://localhost:5174
```

### 4. 访问应用

- **前端界面**: http://localhost:5174
- **后端API**: http://localhost:3001/api
- **健康检查**: http://localhost:3001/health

## 📊 示例数据

项目包含丰富的金融期货行业示例数据：

### 应用分类 (7个)
- **全部应用** - 显示所有23个应用
- **收藏夹** - 用户收藏的应用
- **交易系统** - 期货、股票、外汇、期权交易平台 (4个)
- **风险管理** - 风险监控、合规管理、资金监管 (3个)
- **数据分析** - 市场数据、量化分析、研究报告 (4个)
- **客户服务** - 客户管理、在线客服、开户管理 (3个)
- **办公工具** - 邮箱、文档、会议、项目管理 (5个)

### 外部链接 (4个)
- 上海期货交易所、大连商品交易所
- 郑州商品交易所、中国金融期货交易所

### 创建自定义数据

```bash
# 运行示例数据创建脚本
cd server
node create-sample-data.js
```

## 🔧 配置说明

### 端口配置

- **前端端口**: 5174 (可在 vite.config.ts 中修改)
- **后端端口**: 3001 (可在 server/index.js 中修改)

### Logo 配置

将 logo 文件放置在 `public/logo.png`：
- **建议尺寸**: 64x64px 或更高分辨率
- **格式**: PNG (支持透明背景)

### 环境变量

创建 `.env` 文件：
```env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_TITLE=次席管理平台
```

## 🏗️ 项目架构

### 前后端分离架构

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   React 前端    │ ◄──────────── │  Node.js 后端   │
│  (localhost:5174) │               │ (localhost:3001) │
└─────────────────┘                └─────────────────┘
         │                                   │
         │                                   │
    ┌─────────┐                         ┌─────────┐
    │ Zustand │                         │ JSON 文件│
    │ 状态管理 │                         │ 数据存储 │
    └─────────┘                         └─────────┘
```

### 数据流

1. **前端操作** → API请求 → 后端处理
2. **后端更新** → 内存缓存 → 文件持久化
3. **多客户端** → 共享数据 → 实时同步

### API 设计

- **RESTful API**: 标准的HTTP方法和状态码
- **统一响应**: `{ success: boolean, data: any, error?: string }`
- **错误处理**: 完善的错误捕获和提示
- **CORS支持**: 跨域请求支持

## 📁 项目结构

```
次席管理平台/
├── 📁 前端 (React)
│   ├── src/
│   │   ├── components/          # React 组件
│   │   │   ├── Header.tsx      # 头部导航栏
│   │   │   ├── Sidebar.tsx     # 左侧边栏 (分类+统计)
│   │   │   ├── NavItemGrid.tsx # 应用网格展示
│   │   │   ├── NavItemCard.tsx # 应用卡片组件
│   │   │   ├── AppWithApi.tsx  # API集成主组件
│   │   │   └── *Modal.tsx      # 各种模态框组件
│   │   ├── store/              # 状态管理
│   │   │   ├── useApiStore.ts  # API集成状态管理
│   │   │   └── useAppStore.ts  # 本地状态管理 (备用)
│   │   ├── services/           # 服务层
│   │   │   └── apiClient.ts    # API客户端封装
│   │   ├── utils/              # 工具函数
│   │   │   ├── ipManager.ts    # IP管理和用户识别
│   │   │   ├── accessTracker.ts # 访问统计
│   │   │   └── version.ts      # 版本信息
│   │   ├── types/              # TypeScript 类型
│   │   └── hooks/              # 自定义 Hooks
│   ├── public/                 # 静态资源
│   │   └── logo.png           # 企业Logo
│   └── dist/                   # 构建输出
│
├── 📁 后端 (Node.js)
│   ├── server/
│   │   ├── src/
│   │   │   ├── dataManager.js  # 数据管理器
│   │   │   ├── routes/         # API路由
│   │   │   └── middleware/     # 中间件
│   │   ├── data/               # 数据存储
│   │   │   ├── navItems.json   # 应用数据
│   │   │   ├── categories.json # 分类数据
│   │   │   ├── stats.json      # 统计数据
│   │   │   └── config.json     # 配置数据
│   │   ├── index.js            # 服务器入口
│   │   └── create-sample-data.js # 示例数据脚本
│
├── 📁 文档
│   ├── docs/                   # 主要文档
│   │   ├── README.md          # 文档索引
│   │   ├── ARCHITECTURE.md    # 系统架构
│   │   ├── DEPLOYMENT-GUIDE.md # 部署指南
│   │   └── *.md               # 各种功能和修复文档
│   └── server/docs/           # 后端专用文档
│
└── README.md                   # 项目主说明 (本文件)
```

## 🎯 功能使用

### 👤 普通用户

**浏览应用**:
- 查看所有23个预置的金融期货应用
- 按分类筛选：交易系统、风险管理、数据分析等
- 使用搜索框快速查找应用

**个人操作**:
- ⭐ **收藏应用** - 点击星形图标收藏常用应用
- 👍 **点赞推荐** - 为有用的应用点赞
- 📊 **查看统计** - 左侧边栏显示应用总数、访问统计等
- 🔗 **快速访问** - 点击应用卡片直接跳转

### 👨‍💼 管理员用户

**启用管理模式**:
- 点击右上角的设置图标进入管理模式

**应用管理**:
- ➕ **添加应用** - 创建新的导航应用
- ✏️ **编辑应用** - 修改应用信息、图标、分类等
- 🗑️ **删除应用** - 移除不需要的应用

**分类管理**:
- 📁 **创建分类** - 添加新的应用分类
- 🎨 **自定义分类** - 设置分类颜色、图标、描述
- 🔒 **保护系统分类** - "全部应用"和"收藏夹"不可删除

## 🔧 开发指南

### API 接口

**基础URL**: `http://localhost:3001/api`

**主要接口**:
```bash
# 应用管理
GET    /nav-items          # 获取所有应用
POST   /nav-items          # 创建应用
PUT    /nav-items/:id      # 更新应用
DELETE /nav-items/:id      # 删除应用

# 分类管理
GET    /categories         # 获取所有分类
POST   /categories         # 创建分类
PUT    /categories/:id     # 更新分类
DELETE /categories/:id     # 删除分类

# 统计数据
GET    /stats              # 获取统计信息

# 健康检查
GET    /health             # 服务器状态检查
```

### 添加新功能

**前端开发**:
1. 在 `src/types/index.ts` 定义类型
2. 在 `src/store/useApiStore.ts` 添加状态管理
3. 在 `src/services/apiClient.ts` 添加API调用
4. 创建相应的React组件

**后端开发**:
1. 在 `server/src/routes/` 添加路由
2. 在 `server/src/dataManager.js` 添加数据操作
3. 更新数据模型和验证

### 自定义主题

修改 `src/index.css` 中的CSS变量：
```css
:root {
  --primary-color: #3B82F6;
  --secondary-color: #8B5CF6;
}
```

## 🚀 生产部署

### 构建项目

```bash
# 构建前端
npm run build

# 前端构建产物在 dist/ 目录
```

### 部署方案

**方案一：分离部署**
```bash
# 前端 - 部署到 Nginx/Apache
cp -r dist/* /var/www/html/

# 后端 - 使用 PM2 管理
cd server
pm2 start index.js --name "cjfco-nav-api"
```

**方案二：Docker 部署**
```dockerfile
# 前端 Dockerfile
FROM nginx:alpine
COPY dist /usr/share/nginx/html
EXPOSE 80

# 后端 Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY server/ .
RUN npm install
EXPOSE 3001
CMD ["npm", "start"]
```

### Nginx 配置

```nginx
# 前端配置
server {
    listen 80;
    server_name nav.cjfco.com.cn;
    root /var/www/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }
}

# API 代理配置
server {
    listen 80;
    server_name api.cjfco.com.cn;

    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 环境配置

**生产环境变量**:
```env
NODE_ENV=production
PORT=3001
DATA_DIR=/data/cjfco-nav
```

## � 文档

详细文档请查看 [docs/](./docs/) 目录：

- **[文档索引](./docs/README.md)** - 完整的文档导航
- **[系统架构](./docs/ARCHITECTURE.md)** - 技术架构设计
- **[部署指南](./docs/DEPLOYMENT-GUIDE.md)** - 详细部署说明
- **[开发环境](./docs/DEV-ENVIRONMENT-SETUP.md)** - 开发环境搭建
- **[功能说明](./docs/)** - 各种功能和修复文档

## 🧪 测试

### API 测试

```bash
# 健康检查
curl http://localhost:3001/health

# 获取应用列表
curl http://localhost:3001/api/nav-items

# 获取分类列表
curl http://localhost:3001/api/categories
```

### 前后端连接测试

参考 [前后端连接测试文档](./docs/FRONTEND-BACKEND-TEST.md)

## �📝 更新日志

### v2.0.0 (2025-01-11) - 当前版本
- ✅ **前后端分离架构** - React + Node.js
- ✅ **API集成** - RESTful API设计
- ✅ **数据同步** - 多客户端数据一致性
- ✅ **示例数据** - 23个金融期货应用
- ✅ **企业级功能** - 分类管理、用户权限、访问统计
- ✅ **响应式设计** - 完美适配各种设备
- ✅ **文档完善** - 详细的开发和部署文档

### v1.0.0 (2024-12-01)
- 初始版本发布
- 基础导航功能
- 本地存储方案
- 响应式设计

## 🤝 贡献

欢迎贡献代码和建议！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/NewFeature`)
3. 提交更改 (`git commit -m 'Add NewFeature'`)
4. 推送分支 (`git push origin feature/NewFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

- 📖 **文档**: [docs/README.md](./docs/README.md)
- 🐛 **问题反馈**: 请创建 Issue
- 💡 **功能建议**: 欢迎提出改进建议

---

**次席管理平台** - 专业的金融期货导航系统 🚀

*让企业资源管理更简单高效，为金融期货行业量身定制*
