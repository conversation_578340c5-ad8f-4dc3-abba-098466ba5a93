@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 开始部署 次席管理平台 完整系统...

REM 配置变量
set FRONTEND_PORT=5173
set BACKEND_PORT=3001
set DEPLOY_DIR=C:\cjfconav
set CURRENT_DIR=%cd%

echo 📋 部署配置:
echo    前端端口: %FRONTEND_PORT%
echo    后端端口: %BACKEND_PORT%
echo    部署目录: %DEPLOY_DIR%
echo.

REM 检查依赖
echo 🔍 检查系统依赖...

REM 检查 Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 16+
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js 版本: %NODE_VERSION%

REM 检查 npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm 版本: %NPM_VERSION%
echo.

REM 构建前端
echo 🏗️ 构建前端应用...

echo 📦 安装前端依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)

echo 🔨 构建前端应用...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)

echo ✅ 前端构建完成
echo.

REM 部署后端
echo 🖥️ 部署后端服务...

REM 创建部署目录
if not exist "%DEPLOY_DIR%" mkdir "%DEPLOY_DIR%"
if not exist "%DEPLOY_DIR%\server" mkdir "%DEPLOY_DIR%\server"

echo 📁 复制后端文件...
xcopy /E /I /Y "server\*" "%DEPLOY_DIR%\server\"

REM 安装后端依赖
cd /d "%DEPLOY_DIR%\server"
echo 📦 安装后端依赖...
call npm install --production
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)

REM 创建数据和日志目录
if not exist "data" mkdir data
if not exist "logs" mkdir logs

echo ✅ 后端部署完成
echo.

REM 部署前端
echo 🌐 部署前端应用...

cd /d "%CURRENT_DIR%"
if not exist "%DEPLOY_DIR%\dist" mkdir "%DEPLOY_DIR%\dist"

echo 📁 复制前端文件...
xcopy /E /I /Y "dist\*" "%DEPLOY_DIR%\dist\"

echo ✅ 前端部署完成
echo.

REM 启动后端服务
echo 🚀 启动后端服务...

cd /d "%DEPLOY_DIR%\server"

REM 检查是否已有进程在运行
tasklist /FI "IMAGENAME eq node.exe" /FI "WINDOWTITLE eq *index.js*" 2>nul | find /I "node.exe" >nul
if %errorlevel% equ 0 (
    echo ⚠️ 检测到后端服务已在运行，正在重启...
    taskkill /F /IM node.exe /FI "WINDOWTITLE eq *index.js*" >nul 2>&1
    timeout /t 2 >nul
)

REM 启动后端服务
echo 🔍 启动后端服务...
start /B "" node index.js > logs\server.log 2>&1

REM 等待服务启动
timeout /t 5 >nul

REM 检查后端服务
echo 🔍 检查后端服务状态...
curl -s "http://localhost:%BACKEND_PORT%/health" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端服务启动成功
) else (
    echo ❌ 后端服务启动失败，请检查日志
)

echo.

REM 验证部署
echo 🔍 验证部署...

REM 检查后端健康状态
curl -s "http://localhost:%BACKEND_PORT%/health" | find "ok" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端服务运行正常
) else (
    echo ❌ 后端服务异常
)

echo.

REM 显示部署结果
echo 🎉 部署完成！
echo.
echo 📋 服务信息:
echo    🌐 前端目录: %DEPLOY_DIR%\dist
echo    🖥️ 后端地址: http://localhost:%BACKEND_PORT%
echo    📁 部署目录: %DEPLOY_DIR%
echo    📊 后端日志: %DEPLOY_DIR%\server\logs\server.log
echo.
echo 🔧 管理命令:
echo    查看后端日志: type "%DEPLOY_DIR%\server\logs\server.log"
echo    重启后端服务: cd /d "%DEPLOY_DIR%\server" ^&^& start.bat
echo.
echo 💡 提示: 
echo    - 后端服务已在后台运行
echo    - 前端文件已部署到 %DEPLOY_DIR%\dist
echo    - 您需要配置 Web 服务器（如 IIS 或 nginx）来提供前端服务
echo    - 前端应该配置代理将 /api 请求转发到 http://localhost:%BACKEND_PORT%
echo.

cd /d "%CURRENT_DIR%"
pause
