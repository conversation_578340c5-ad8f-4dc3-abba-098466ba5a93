import { NavItem, Category } from '../types';

/**
 * API客户端配置
 */
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

/**
 * API响应接口
 */
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  total?: number;
}

/**
 * HTTP请求工具类
 */
class HttpClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const config = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return data;
    } catch (error) {
      console.error(`API请求失败 [${endpoint}]:`, error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

/**
 * API客户端类
 */
export class ApiClient {
  private http: HttpClient;

  constructor() {
    this.http = new HttpClient(API_BASE_URL);
  }

  // ==================== 导航项目相关 ====================

  /**
   * 获取所有导航项目
   */
  async getNavItems(): Promise<NavItem[]> {
    const response = await this.http.get<NavItem[]>('/nav-items');
    return response.data || [];
  }

  /**
   * 添加导航项目
   */
  async addNavItem(item: Omit<NavItem, 'id' | 'createdAt' | 'updatedAt' | 'accessCount'>): Promise<NavItem> {
    const response = await this.http.post<NavItem>('/nav-items', item);
    if (!response.data) {
      throw new Error('添加导航项目失败');
    }
    return response.data;
  }

  /**
   * 更新导航项目
   */
  async updateNavItem(id: string, updates: Partial<NavItem>): Promise<NavItem> {
    const response = await this.http.put<NavItem>(`/nav-items/${id}`, updates);
    if (!response.data) {
      throw new Error('更新导航项目失败');
    }
    return response.data;
  }

  /**
   * 删除导航项目
   */
  async deleteNavItem(id: string): Promise<NavItem> {
    const response = await this.http.delete<NavItem>(`/nav-items/${id}`);
    if (!response.data) {
      throw new Error('删除导航项目失败');
    }
    return response.data;
  }

  /**
   * 增加访问次数
   */
  async incrementAccessCount(id: string): Promise<NavItem> {
    const response = await this.http.post<NavItem>(`/nav-items/${id}/access`);
    if (!response.data) {
      throw new Error('更新访问次数失败');
    }
    return response.data;
  }

  // ==================== 分类相关 ====================

  /**
   * 获取所有分类
   */
  async getCategories(): Promise<Category[]> {
    const response = await this.http.get<Category[]>('/categories');
    return response.data || [];
  }

  /**
   * 添加分类
   */
  async addCategory(category: Omit<Category, 'id' | 'createdAt' | 'itemCount'>): Promise<Category> {
    const response = await this.http.post<Category>('/categories', category);
    if (!response.data) {
      throw new Error('添加分类失败');
    }
    return response.data;
  }

  /**
   * 更新分类
   */
  async updateCategory(id: string, updates: Partial<Category>): Promise<Category> {
    const response = await this.http.put<Category>(`/categories/${id}`, updates);
    if (!response.data) {
      throw new Error('更新分类失败');
    }
    return response.data;
  }

  /**
   * 删除分类
   */
  async deleteCategory(id: string): Promise<Category> {
    const response = await this.http.delete<Category>(`/categories/${id}`);
    if (!response.data) {
      throw new Error('删除分类失败');
    }
    return response.data;
  }

  // ==================== 统计数据相关 ====================

  /**
   * 获取统计数据
   */
  async getStats(): Promise<any> {
    const response = await this.http.get<any>('/stats');
    return response.data || {};
  }

  // ==================== 配置相关 ====================

  /**
   * 获取配置
   */
  async getConfig(): Promise<any> {
    const response = await this.http.get<any>('/config');
    return response.data || {};
  }

  /**
   * 更新配置
   */
  async updateConfig(updates: any): Promise<any> {
    const response = await this.http.put<any>('/config', updates);
    if (!response.data) {
      throw new Error('更新配置失败');
    }
    return response.data;
  }

  // ==================== 数据管理相关 ====================

  /**
   * 手动保存数据
   */
  async saveData(): Promise<void> {
    await this.http.post<void>('/save');
  }

  /**
   * 获取服务器状态
   */
  async getServerStatus(): Promise<any> {
    // 使用健康检查接口，不通过 /api 前缀
    const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
    const data = await response.json();
    return data || {};
  }

  // ==================== 连接检查 ====================

  /**
   * 检查服务器连接
   */
  async checkConnection(): Promise<boolean> {
    try {
      // 使用健康检查接口，不通过 /api 前缀
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
      return response.ok;
    } catch (error) {
      console.warn('服务器连接失败，将使用本地存储模式');
      return false;
    }
  }
}

/**
 * 数据同步服务 - 支持在线/离线模式
 */
export class DataSyncService {
  private apiClient: ApiClient;
  private isOnline: boolean = false;
  private syncQueue: Array<{ action: string; data: any }> = [];

  constructor() {
    this.apiClient = new ApiClient();
    this.checkOnlineStatus();
  }

  /**
   * 检查在线状态
   */
  async checkOnlineStatus(): Promise<boolean> {
    try {
      this.isOnline = await this.apiClient.checkConnection();
      return this.isOnline;
    } catch (error) {
      this.isOnline = false;
      return false;
    }
  }

  /**
   * 获取导航项目（在线/离线兼容）
   */
  async getNavItems(): Promise<NavItem[]> {
    if (this.isOnline) {
      try {
        const items = await this.apiClient.getNavItems();
        // 同步到本地存储作为备份
        localStorage.setItem('navItems', JSON.stringify(items));
        return items;
      } catch (error) {
        console.warn('从服务器获取数据失败，使用本地数据');
        this.isOnline = false;
      }
    }

    // 离线模式：从本地存储获取
    const localData = localStorage.getItem('navItems');
    return localData ? JSON.parse(localData) : [];
  }

  /**
   * 获取分类（在线/离线兼容）
   */
  async getCategories(): Promise<Category[]> {
    if (this.isOnline) {
      try {
        const categories = await this.apiClient.getCategories();
        // 同步到本地存储作为备份
        localStorage.setItem('categories', JSON.stringify(categories));
        return categories;
      } catch (error) {
        console.warn('从服务器获取分类失败，使用本地数据');
        this.isOnline = false;
      }
    }

    // 离线模式：从本地存储获取
    const localData = localStorage.getItem('categories');
    return localData ? JSON.parse(localData) : [];
  }

  /**
   * 删除导航项目（在线/离线兼容）
   */
  async deleteNavItem(id: string): Promise<NavItem> {
    if (this.isOnline) {
      try {
        return await this.apiClient.deleteNavItem(id);
      } catch (error) {
        console.warn('在线删除失败，加入同步队列');
        this.isOnline = false;
      }
    }

    // 离线模式：添加到同步队列
    this.syncQueue.push({ action: 'deleteNavItem', data: { id } });

    // 更新本地存储
    const items = await this.getNavItems();
    const itemIndex = items.findIndex(item => item.id === id);
    if (itemIndex !== -1) {
      const deletedItem = items.splice(itemIndex, 1)[0];
      localStorage.setItem('navItems', JSON.stringify(items));
      return deletedItem;
    }

    throw new Error('项目不存在');
  }

  /**
   * 更新导航项目（在线/离线兼容）
   */
  async updateNavItem(id: string, updates: Partial<NavItem>): Promise<NavItem> {
    if (this.isOnline) {
      try {
        return await this.apiClient.updateNavItem(id, updates);
      } catch (error) {
        console.warn('在线更新失败，加入同步队列');
        this.isOnline = false;
      }
    }

    // 离线模式：添加到同步队列
    this.syncQueue.push({ action: 'updateNavItem', data: { id, updates } });

    // 更新本地存储
    const items = await this.getNavItems();
    const itemIndex = items.findIndex(item => item.id === id);
    if (itemIndex !== -1) {
      const updatedItem = { ...items[itemIndex], ...updates, updatedAt: new Date().toISOString() };
      items[itemIndex] = updatedItem;
      localStorage.setItem('navItems', JSON.stringify(items));
      return updatedItem;
    }

    throw new Error('项目不存在');
  }

  /**
   * 增加访问次数（在线/离线兼容）
   */
  async incrementAccessCount(id: string): Promise<NavItem> {
    if (this.isOnline) {
      try {
        return await this.apiClient.incrementAccessCount(id);
      } catch (error) {
        console.warn('在线更新访问次数失败');
        this.isOnline = false;
      }
    }

    // 离线模式：本地更新
    const items = await this.getNavItems();
    const itemIndex = items.findIndex(item => item.id === id);
    if (itemIndex !== -1) {
      items[itemIndex].accessCount = (items[itemIndex].accessCount || 0) + 1;
      items[itemIndex].lastAccessed = new Date().toISOString();
      localStorage.setItem('navItems', JSON.stringify(items));
      return items[itemIndex];
    }

    throw new Error('项目不存在');
  }

  /**
   * 添加分类（在线/离线兼容）
   */
  async addCategory(category: Omit<Category, 'id' | 'createdAt' | 'itemCount'>): Promise<Category> {
    if (this.isOnline) {
      try {
        return await this.apiClient.addCategory(category);
      } catch (error) {
        console.warn('在线添加分类失败，加入同步队列');
        this.isOnline = false;
      }
    }

    // 离线模式：添加到同步队列
    const newCategory: Category = {
      ...category,
      id: this.generateTempId(),
      itemCount: 0
    };

    this.syncQueue.push({ action: 'addCategory', data: newCategory });

    // 更新本地存储
    const categories = await this.getCategories();
    categories.push(newCategory);
    localStorage.setItem('categories', JSON.stringify(categories));

    return newCategory;
  }

  /**
   * 更新分类（在线/离线兼容）
   */
  async updateCategory(id: string, updates: Partial<Category>): Promise<Category> {
    if (this.isOnline) {
      try {
        return await this.apiClient.updateCategory(id, updates);
      } catch (error) {
        console.warn('在线更新分类失败，加入同步队列');
        this.isOnline = false;
      }
    }

    // 离线模式：添加到同步队列
    this.syncQueue.push({ action: 'updateCategory', data: { id, updates } });

    // 更新本地存储
    const categories = await this.getCategories();
    const categoryIndex = categories.findIndex(cat => cat.id === id);
    if (categoryIndex !== -1) {
      const updatedCategory = { ...categories[categoryIndex], ...updates };
      categories[categoryIndex] = updatedCategory;
      localStorage.setItem('categories', JSON.stringify(categories));
      return updatedCategory;
    }

    throw new Error('分类不存在');
  }

  /**
   * 删除分类（在线/离线兼容）
   */
  async deleteCategory(id: string): Promise<Category> {
    if (this.isOnline) {
      try {
        return await this.apiClient.deleteCategory(id);
      } catch (error) {
        console.warn('在线删除分类失败，加入同步队列');
        this.isOnline = false;
      }
    }

    // 离线模式：添加到同步队列
    this.syncQueue.push({ action: 'deleteCategory', data: { id } });

    // 更新本地存储
    const categories = await this.getCategories();
    const categoryIndex = categories.findIndex(cat => cat.id === id);
    if (categoryIndex !== -1) {
      const deletedCategory = categories.splice(categoryIndex, 1)[0];
      localStorage.setItem('categories', JSON.stringify(categories));
      return deletedCategory;
    }

    throw new Error('分类不存在');
  }

  /**
   * 添加导航项目（在线/离线兼容）
   */
  async addNavItem(item: Omit<NavItem, 'id' | 'createdAt' | 'updatedAt' | 'accessCount'>): Promise<NavItem> {
    if (this.isOnline) {
      try {
        return await this.apiClient.addNavItem(item);
      } catch (error) {
        console.warn('在线添加失败，加入同步队列');
        this.isOnline = false;
      }
    }

    // 离线模式：添加到同步队列
    const newItem: NavItem = {
      ...item,
      id: this.generateTempId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      accessCount: 0
    };

    this.syncQueue.push({ action: 'addNavItem', data: newItem });

    // 更新本地存储
    const items = await this.getNavItems();
    items.push(newItem);
    localStorage.setItem('navItems', JSON.stringify(items));

    return newItem;
  }

  /**
   * 同步离线数据到服务器
   */
  async syncOfflineData(): Promise<void> {
    if (!this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    console.log(`开始同步 ${this.syncQueue.length} 个离线操作`);

    for (const operation of this.syncQueue) {
      try {
        switch (operation.action) {
          case 'addNavItem':
            await this.apiClient.addNavItem(operation.data);
            break;
          // 可以添加更多操作类型
        }
      } catch (error) {
        console.error('同步操作失败:', operation, error);
      }
    }

    // 清空同步队列
    this.syncQueue = [];
    console.log('离线数据同步完成');
  }

  /**
   * 生成临时ID
   */
  private generateTempId(): string {
    return 'temp_' + Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * 获取在线状态
   */
  getOnlineStatus(): boolean {
    return this.isOnline;
  }

  /**
   * 获取待同步操作数量
   */
  getPendingSyncCount(): number {
    return this.syncQueue.length;
  }
}

// 创建全局实例
export const apiClient = new ApiClient();
export const dataSyncService = new DataSyncService();

// 导出类型
export type { ApiResponse };
